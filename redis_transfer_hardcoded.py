#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import redis
import time

# ==================== 配置区域 ====================
# 在这里修改你的Redis配置

# 源Redis配置
SOURCE_CONFIG = {
    'host': 'localhost',        # 源Redis主机地址
    'port': 6379,              # 源Redis端口
    'password': None,          # 源Redis密码，无密码设为None
    'db': 0,                   # 源Redis数据库编号
}

# 目标Redis配置
TARGET_CONFIG = {
    'host': 'localhost',        # 目标Redis主机地址
    'port': 6379,              # 目标Redis端口
    'password': None,          # 目标Redis密码，无密码设为None
    'db': 7,                   # 目标Redis数据库编号
}

# 转移选项
TRANSFER_OPTIONS = {
    'copy_mode': True,         # True=复制模式(保留源数据), False=移动模式(删除源数据)
    'overwrite': False,        # True=覆盖已存在的键, False=跳过已存在的键
    'batch_size': 100,         # 批处理大小
    'show_progress': True,     # 是否显示详细进度
}

# ==================== 代码区域 ====================

class RedisTransferHardcoded:
    def __init__(self):
        """初始化Redis连接"""
        print("Redis数据转移工具")
        print("="*50)
        
        # 创建源Redis连接
        self.source_client = redis.Redis(
            host=SOURCE_CONFIG['host'],
            port=SOURCE_CONFIG['port'],
            password=SOURCE_CONFIG['password'],
            db=SOURCE_CONFIG['db'],
            decode_responses=False
        )
        
        # 创建目标Redis连接
        self.target_client = redis.Redis(
            host=TARGET_CONFIG['host'],
            port=TARGET_CONFIG['port'],
            password=TARGET_CONFIG['password'],
            db=TARGET_CONFIG['db'],
            decode_responses=False
        )
        
        self.show_config()
    
    def show_config(self):
        """显示当前配置"""
        print(f"源Redis: {SOURCE_CONFIG['host']}:{SOURCE_CONFIG['port']} DB{SOURCE_CONFIG['db']}")
        print(f"目标Redis: {TARGET_CONFIG['host']}:{TARGET_CONFIG['port']} DB{TARGET_CONFIG['db']}")
        print(f"模式: {'复制' if TRANSFER_OPTIONS['copy_mode'] else '移动'}")
        print(f"覆盖: {'是' if TRANSFER_OPTIONS['overwrite'] else '否'}")
        print("-" * 50)
    
    def test_connections(self):
        """测试Redis连接"""
        try:
            self.source_client.ping()
            print(f"✓ 源Redis连接成功")
        except Exception as e:
            print(f"✗ 源Redis连接失败: {e}")
            return False
            
        try:
            self.target_client.ping()
            print(f"✓ 目标Redis连接成功")
        except Exception as e:
            print(f"✗ 目标Redis连接失败: {e}")
            return False
            
        return True
    
    def get_database_info(self):
        """获取数据库信息"""
        try:
            source_size = self.source_client.dbsize()
            target_size = self.target_client.dbsize()
            return source_size, target_size
        except Exception as e:
            print(f"获取数据库信息失败: {e}")
            return 0, 0
    
    def transfer_data(self):
        """执行数据转移"""
        copy_mode = TRANSFER_OPTIONS['copy_mode']
        overwrite = TRANSFER_OPTIONS['overwrite']
        batch_size = TRANSFER_OPTIONS['batch_size']
        show_progress = TRANSFER_OPTIONS['show_progress']
        
        mode_text = "复制" if copy_mode else "移动"
        print(f"\n开始{mode_text}数据...")
        
        # 获取所有键
        print("正在扫描源数据库...")
        all_keys = []
        cursor = 0
        
        while True:
            cursor, keys = self.source_client.scan(cursor=cursor, count=batch_size)
            all_keys.extend(keys)
            if cursor == 0:
                break
        
        total_keys = len(all_keys)
        print(f"找到 {total_keys} 个键需要{mode_text}")
        
        if total_keys == 0:
            print("源数据库为空，无需转移")
            return
        
        # 开始转移
        transferred = 0
        failed = 0
        skipped = 0
        
        start_time = time.time()
        
        for i, key in enumerate(all_keys, 1):
            try:
                # 检查目标数据库是否已存在该键
                if self.target_client.exists(key) and not overwrite:
                    if show_progress:
                        print(f"[{i}/{total_keys}] 跳过键: {key.decode('utf-8', errors='ignore')} (已存在)")
                    skipped += 1
                    continue
                
                # 使用DUMP和RESTORE进行转移
                dump_data = self.source_client.dump(key)
                if dump_data is None:
                    if show_progress:
                        print(f"[{i}/{total_keys}] 跳过键: {key.decode('utf-8', errors='ignore')} (无法导出)")
                    skipped += 1
                    continue
                
                # 获取TTL
                ttl = self.source_client.pttl(key)
                if ttl == -2:  # 键不存在
                    skipped += 1
                    continue
                elif ttl == -1:  # 没有过期时间
                    ttl = 0
                
                # 如果需要覆盖，先删除目标键
                if overwrite and self.target_client.exists(key):
                    self.target_client.delete(key)
                
                # 转移数据到目标Redis
                self.target_client.restore(key, ttl, dump_data)
                
                # 如果是移动模式，删除源数据
                if not copy_mode:
                    self.source_client.delete(key)
                
                transferred += 1
                
                # 显示进度
                if show_progress:
                    action_text = "复制" if copy_mode else "移动"
                    print(f"[{i}/{total_keys}] {action_text}键: {key.decode('utf-8', errors='ignore')}")
                elif i % 100 == 0 or i == total_keys:
                    print(f"进度: {i}/{total_keys} ({i/total_keys*100:.1f}%)")
                
            except Exception as e:
                failed += 1
                if show_progress:
                    print(f"[{i}/{total_keys}] 失败: {key.decode('utf-8', errors='ignore')} - {e}")
        
        end_time = time.time()
        
        # 输出结果
        print("\n" + "="*50)
        print(f"{mode_text}完成!")
        print(f"总键数: {total_keys}")
        print(f"成功{mode_text}: {transferred}")
        print(f"跳过: {skipped}")
        print(f"失败: {failed}")
        print(f"耗时: {end_time - start_time:.2f} 秒")
        
        # 验证结果
        source_size, target_size = self.get_database_info()
        print(f"\n最终状态:")
        if copy_mode:
            print(f"源数据库键数: {source_size} (数据保留)")
        else:
            print(f"源数据库键数: {source_size} (数据已{mode_text})")
        print(f"目标数据库键数: {target_size}")
    
    def run(self):
        """运行转移程序"""
        # 测试连接
        if not self.test_connections():
            print("连接测试失败，程序退出")
            return
        
        # 获取当前状态
        source_size, target_size = self.get_database_info()
        print(f"\n当前状态:")
        print(f"源数据库键数: {source_size}")
        print(f"目标数据库键数: {target_size}")
        
        if source_size == 0:
            print("源数据库为空，无需转移")
            return
        
        # 确认执行
        mode_text = "移动" if not TRANSFER_OPTIONS['copy_mode'] else "复制"
        action_text = "覆盖" + mode_text if TRANSFER_OPTIONS['overwrite'] else mode_text
        
        print(f"\n即将执行: {action_text} {source_size} 个键")
        confirm = input("确认执行? (y/N): ")
        
        if confirm.lower() != 'y':
            print("取消转移")
            return
        
        # 执行转移
        self.transfer_data()

def main():
    """主函数"""
    print("注意: 请在脚本顶部修改Redis配置后再运行!")
    print("当前配置:")
    print(f"  源: {SOURCE_CONFIG}")
    print(f"  目标: {TARGET_CONFIG}")
    print(f"  选项: {TRANSFER_OPTIONS}")
    print()
    
    # 创建并运行转移器
    transfer = RedisTransferHardcoded()
    transfer.run()

if __name__ == "__main__":
    main()
