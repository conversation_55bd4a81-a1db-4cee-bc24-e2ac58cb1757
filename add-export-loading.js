/**
 * 批量为导出按钮添加 loading 状态的脚本
 * 这个脚本会自动识别并修改包含导出功能的 Vue 文件
 */

const fs = require('fs');
const path = require('path');

// 需要处理的文件列表
const filesToProcess = [
  'src/views/unitedDept/police.vue',
  'src/components/biaoZhunBanChuZhongStudentList/index.vue',
  'src/components/biaoZhunBanXiaoXueStudentList/index.vue',
  'src/views/counties/feiXiang/enrollment/biYeXiaoXueShenHe/index.vue',
  'src/views/enrollment/primaryQW/index.vue',
  'src/views/delaySchool/index.vue',
  'src/views/unitedDept/property.vue',
  'src/views/counties/linZhang-d/enrollment/junior/index.vue',
  'src/views/outsideSchool/index.vue',
  'src/views/dispatch/yongNian/result.vue',
  'src/views/dataVerify/propertyZhuJian.vue',
  'src/views/dispatch/qiuXian/luQuJieGuo.vue',
  'src/views/unitedDept/policeResidencePermit.vue',
  'src/views/enrollment/graduate/index.vue',
  'src/components/privatePrimaryEnroll/list.vue',
  'src/components/privateJuniorEnroll/list.vue',
  'src/views/privateEnrollment/junior/list.vue',
  'src/views/counties/qiuxian/enrollment/junior/index.vue',
  'src/views/counties/chengAn/dianLiJuShenHe/index.vue',
  'src/views/counties/quZhou/shiChangJianDuGuanLiJu/index.vue',
  'src/views/counties/feiXiang/enrollment/junior1/index.vue',
  'src/views/dispatch/linZhang/luQuJieGuoSchool.vue',
  'src/views/counties/chengAn/xingZhengShenHe/index.vue',
  'src/views/counties/yongNian/enrollment/junior/index.vue'
];

// 导出方法名称模式
const exportMethodPatterns = [
  'exportList',
  'exportEnrollInfo',
  'exportData',
  'exportHouseholdSurvey',
  'exportRes'
];

// 导出按钮文本模式
const exportButtonPatterns = [
  '导出',
  '下载',
  'export',
  'Export'
];

/**
 * 检查文件是否包含导出功能
 */
function hasExportFunction(content) {
  return exportMethodPatterns.some(method => content.includes(method)) ||
         exportButtonPatterns.some(text => content.includes(`>${text}`));
}

/**
 * 为单个文件添加导出 loading 功能
 */
function addExportLoadingToFile(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`文件不存在: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  
  if (!hasExportFunction(content)) {
    console.log(`文件不包含导出功能: ${filePath}`);
    return false;
  }

  let modified = false;
  let newContent = content;

  // 1. 在 data 中添加 loading 状态变量
  if (!content.includes('exportLoading:') && !content.includes('exportLoading =')) {
    // 查找 data() 函数中的 return 语句
    const dataReturnRegex = /(data\(\)\s*{\s*return\s*{[^}]*)(}\s*;?\s*})/s;
    const match = content.match(dataReturnRegex);
    
    if (match) {
      const beforeClosing = match[1];
      const closing = match[2];
      
      // 检查是否已经有其他 loading 变量，如果有就添加在附近
      if (beforeClosing.includes('Loading:')) {
        newContent = newContent.replace(dataReturnRegex, 
          `${beforeClosing},
      exportLoading: false,${closing}`);
      } else {
        newContent = newContent.replace(dataReturnRegex, 
          `${beforeClosing},
      exportLoading: false${closing}`);
      }
      modified = true;
      console.log(`✓ 添加 exportLoading 数据属性: ${filePath}`);
    }
  }

  // 2. 为导出按钮添加 :loading 属性
  exportButtonPatterns.forEach(buttonText => {
    const buttonRegex = new RegExp(`(<el-button[^>]*@click="(export[^"]*)"[^>]*)(>${buttonText}[^<]*</el-button>)`, 'gi');
    newContent = newContent.replace(buttonRegex, (match, buttonStart, methodName, buttonEnd) => {
      if (!buttonStart.includes(':loading=')) {
        modified = true;
        console.log(`✓ 添加按钮 loading 属性: ${filePath} - ${methodName}`);
        return `${buttonStart}
            :loading="exportLoading"${buttonEnd}`;
      }
      return match;
    });
  });

  // 3. 修改导出方法添加 loading 控制
  exportMethodPatterns.forEach(methodName => {
    const methodRegex = new RegExp(`(${methodName}\\(\\)[^{]*{)([^}]*this\\.\\$download[^}]*)(}[^}]*})`, 's');
    newContent = newContent.replace(methodRegex, (match, methodStart, methodBody, methodEnd) => {
      if (!methodBody.includes('exportLoading')) {
        modified = true;
        console.log(`✓ 修改导出方法添加 loading 控制: ${filePath} - ${methodName}`);
        
        // 添加 loading 控制逻辑
        const newMethodBody = methodBody
          .replace(/(\s*)(this\.\$download)/, `$1// 防止重复点击
$1if (this.exportLoading) {
$1  return;
$1}

$1this.exportLoading = true;
$1$2`)
          .replace(/(\.then\([^)]*\)\s*=>\s*{)([^}]*)(}\s*\))/s, 
            `$1$2
        this.exportLoading = false;$3`)
          .replace(/(\.then\([^}]*)(}\s*\))/s, 
            `$1
        this.exportLoading = false;
      }).catch((error) => {
        this.exportLoading = false;
        this.$message.error("导出失败");
        console.error("导出错误:", error);$2`);
        
        return `${methodStart}${newMethodBody}${methodEnd}`;
      }
      return match;
    });
  });

  if (modified) {
    fs.writeFileSync(filePath, newContent, 'utf8');
    console.log(`✅ 文件修改完成: ${filePath}`);
    return true;
  } else {
    console.log(`⚪ 文件无需修改: ${filePath}`);
    return false;
  }
}

/**
 * 批量处理所有文件
 */
function processAllFiles() {
  console.log('开始批量添加导出 loading 功能...\n');
  
  let processedCount = 0;
  let modifiedCount = 0;

  filesToProcess.forEach(filePath => {
    console.log(`\n处理文件: ${filePath}`);
    processedCount++;
    
    if (addExportLoadingToFile(filePath)) {
      modifiedCount++;
    }
  });

  console.log(`\n\n📊 处理完成统计:`);
  console.log(`总处理文件数: ${processedCount}`);
  console.log(`成功修改文件数: ${modifiedCount}`);
  console.log(`无需修改文件数: ${processedCount - modifiedCount}`);
}

// 执行批量处理
if (require.main === module) {
  processAllFiles();
}

module.exports = {
  addExportLoadingToFile,
  processAllFiles
};
