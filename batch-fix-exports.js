#!/usr/bin/env node

/**
 * 批量修复导出按钮 loading 状态的 Node.js 脚本
 * 使用方法: node batch-fix-exports.js
 */

const fs = require('fs');
const path = require('path');

// 需要处理的文件列表（剩余未处理的文件）
const remainingFiles = [
  'src/views/dataVerify/propertyZhuJian.vue',
  'src/views/unitedDept/policeResidencePermit.vue',
  'src/components/biaoZhunBanChuZhongStudentList/index.vue',
  'src/components/biaoZhunBanXiaoXueStudentList/index.vue',
  'src/views/enrollment/primaryQW/index.vue',
  'src/views/delaySchool/index.vue',
  'src/views/outsideSchool/index.vue',
  'src/views/enrollment/graduate/index.vue',
  'src/views/counties/feiXiang/enrollment/biYeXiaoXueShenHe/index.vue',
  'src/views/counties/linZhang-d/enrollment/junior/index.vue',
  'src/views/counties/qiuxian/enrollment/junior/index.vue',
  'src/views/counties/chengAn/dianLiJuShenHe/index.vue',
  'src/views/counties/quZhou/shiChangJianDuGuanLiJu/index.vue',
  'src/views/counties/feiXiang/enrollment/junior1/index.vue',
  'src/views/counties/chengAn/xingZhengShenHe/index.vue',
  'src/views/counties/yongNian/enrollment/junior/index.vue',
  'src/views/dispatch/yongNian/result.vue',
  'src/views/dispatch/qiuXian/luQuJieGuo.vue',
  'src/views/dispatch/linZhang/luQuJieGuoSchool.vue',
  'src/components/privatePrimaryEnroll/list.vue',
  'src/components/privateJuniorEnroll/list.vue',
  'src/views/privateEnrollment/junior/list.vue'
];

/**
 * 修复单个文件的导出功能
 */
function fixExportLoadingInFile(filePath) {
  console.log(`\n🔧 处理文件: ${filePath}`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`❌ 文件不存在: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  let originalContent = content;
  let modified = false;

  // 1. 检查是否已经有 exportLoading
  if (content.includes('exportLoading:') || content.includes('exportLoading =')) {
    console.log(`⚪ 文件已包含 exportLoading: ${filePath}`);
    return false;
  }

  // 2. 查找导出相关的按钮和方法
  const exportMethods = [];
  const exportButtonRegex = /@click="(export[^"]*)"[^>]*>([^<]*导出[^<]*|[^<]*下载[^<]*)</gi;
  let match;
  
  while ((match = exportButtonRegex.exec(content)) !== null) {
    exportMethods.push(match[1]);
    console.log(`📤 找到导出方法: ${match[1]}`);
  }

  if (exportMethods.length === 0) {
    console.log(`⚪ 未找到导出功能: ${filePath}`);
    return false;
  }

  // 3. 在 data() 中添加 exportLoading
  const dataReturnRegex = /(data\(\)\s*{\s*return\s*{[^}]*)(}\s*;?\s*})/s;
  const dataMatch = content.match(dataReturnRegex);
  
  if (dataMatch) {
    const beforeClosing = dataMatch[1];
    const closing = dataMatch[2];
    
    // 添加 exportLoading 属性
    content = content.replace(dataReturnRegex, 
      `${beforeClosing},
      exportLoading: false${closing}`);
    modified = true;
    console.log(`✅ 添加 exportLoading 数据属性`);
  }

  // 4. 为导出按钮添加 :loading 属性
  exportMethods.forEach(methodName => {
    const buttonRegex = new RegExp(`(<el-button[^>]*@click="${methodName}"[^>]*)(>[^<]*</el-button>)`, 'gi');
    content = content.replace(buttonRegex, (match, buttonStart, buttonEnd) => {
      if (!buttonStart.includes(':loading=')) {
        modified = true;
        console.log(`✅ 为按钮添加 loading 属性: ${methodName}`);
        return `${buttonStart}
          :loading="exportLoading"${buttonEnd}`;
      }
      return match;
    });
  });

  // 5. 修改导出方法添加 loading 控制
  exportMethods.forEach(methodName => {
    const methodRegex = new RegExp(`(${methodName}\\(\\)[^{]*{)([^}]*this\\.\\$download[^}]*?)(}[^}]*})`, 's');
    content = content.replace(methodRegex, (match, methodStart, methodBody, methodEnd) => {
      if (!methodBody.includes('exportLoading')) {
        modified = true;
        console.log(`✅ 修改导出方法添加 loading 控制: ${methodName}`);
        
        // 添加防重复点击检查
        let newMethodBody = methodBody.replace(
          /(\s*)(this\.\$download)/,
          `$1// 防止重复点击
$1if (this.exportLoading) {
$1  return;
$1}

$1this.exportLoading = true;
$1$2`
        );

        // 添加成功和失败的 loading 重置
        newMethodBody = newMethodBody.replace(
          /(\.then\([^)]*\)\s*=>\s*{)([^}]*)(}\s*\))/s,
          `$1$2
        this.exportLoading = false;$3.catch((error) => {
        this.exportLoading = false;
        this.$message.error("导出失败");
        console.error("导出错误:", error);
      })`
        );

        return `${methodStart}${newMethodBody}${methodEnd}`;
      }
      return match;
    });
  });

  // 6. 保存修改后的文件
  if (modified && content !== originalContent) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ 文件修改完成: ${filePath}`);
    return true;
  } else {
    console.log(`⚪ 文件无需修改: ${filePath}`);
    return false;
  }
}

/**
 * 批量处理所有文件
 */
function batchFixExports() {
  console.log('🚀 开始批量修复导出按钮 loading 状态...\n');
  
  let processedCount = 0;
  let modifiedCount = 0;
  let errorCount = 0;

  remainingFiles.forEach(filePath => {
    try {
      processedCount++;
      if (fixExportLoadingInFile(filePath)) {
        modifiedCount++;
      }
    } catch (error) {
      errorCount++;
      console.error(`❌ 处理文件时出错: ${filePath}`, error.message);
    }
  });

  console.log(`\n\n📊 批量处理完成统计:`);
  console.log(`📁 总处理文件数: ${processedCount}`);
  console.log(`✅ 成功修改文件数: ${modifiedCount}`);
  console.log(`⚪ 无需修改文件数: ${processedCount - modifiedCount - errorCount}`);
  console.log(`❌ 处理失败文件数: ${errorCount}`);
  
  if (modifiedCount > 0) {
    console.log(`\n🎉 修复完成！已为 ${modifiedCount} 个文件添加导出 loading 功能。`);
  }
}

// 执行批量处理
if (require.main === module) {
  batchFixExports();
}

module.exports = {
  fixExportLoadingInFile,
  batchFixExports
};
