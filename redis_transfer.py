#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import redis
import sys
import time
import json
from typing import Optional

class RedisTransfer:
    def __init__(self, source_config, target_config):
        """
        初始化Redis连接
        source_config: 源Redis配置 {'host': 'localhost', 'port': 6379, 'password': None, 'db': 0}
        target_config: 目标Redis配置 {'host': 'localhost', 'port': 6379, 'password': None, 'db': 7}
        """
        self.source_config = source_config
        self.target_config = target_config
        
        # 创建源Redis连接
        self.source_client = redis.Redis(
            host=source_config['host'],
            port=source_config['port'],
            password=source_config.get('password'),
            db=source_config['db'],
            decode_responses=False  # 保持原始数据格式
        )
        
        # 创建目标Redis连接
        self.target_client = redis.Redis(
            host=target_config['host'],
            port=target_config['port'],
            password=target_config.get('password'),
            db=target_config['db'],
            decode_responses=False
        )
        
    def test_connections(self):
        """测试Redis连接"""
        try:
            self.source_client.ping()
            print(f"✓ 成功连接到源Redis服务器 {self.source_config['host']}:{self.source_config['port']} DB{self.source_config['db']}")
        except Exception as e:
            print(f"✗ 连接源Redis失败: {e}")
            return False
            
        try:
            self.target_client.ping()
            print(f"✓ 成功连接到目标Redis服务器 {self.target_config['host']}:{self.target_config['port']} DB{self.target_config['db']}")
        except Exception as e:
            print(f"✗ 连接目标Redis失败: {e}")
            return False
            
        return True
    
    def get_db_size(self, client):
        """获取指定Redis客户端的键数量"""
        try:
            return client.dbsize()
        except Exception as e:
            print(f"获取数据库大小失败: {e}")
            return 0
    
    def transfer_data(self, batch_size=100, overwrite=False, copy_mode=True):
        """
        在两个Redis实例之间转移数据
        copy_mode: True=复制模式(保留源数据), False=移动模式(删除源数据)
        """
        mode_text = "复制" if copy_mode else "移动"
        print(f"开始{mode_text}数据:")
        print(f"源: {self.source_config['host']}:{self.source_config['port']} DB{self.source_config['db']}")
        print(f"目标: {self.target_config['host']}:{self.target_config['port']} DB{self.target_config['db']}")
        
        if overwrite:
            print("模式: 覆盖已存在的键")
        else:
            print("模式: 跳过已存在的键")
        
        # 获取所有键
        print("正在扫描源数据库的所有键...")
        all_keys = []
        cursor = 0
        
        while True:
            cursor, keys = self.source_client.scan(cursor=cursor, count=batch_size)
            all_keys.extend(keys)
            if cursor == 0:
                break
        
        total_keys = len(all_keys)
        print(f"找到 {total_keys} 个键需要{mode_text}")
        
        if total_keys == 0:
            print(f"源数据库为空，无需{mode_text}")
            return
        
        # 开始转移
        transferred = 0
        failed = 0
        skipped = 0
        
        for i, key in enumerate(all_keys, 1):
            try:
                # 检查目标数据库是否已存在该键
                if self.target_client.exists(key) and not overwrite:
                    print(f"[{i}/{total_keys}] 跳过键 {key.decode('utf-8', errors='ignore')} (目标库已存在)")
                    skipped += 1
                    continue
                
                # 使用DUMP和RESTORE进行完整转移
                dump_data = self.source_client.dump(key)
                if dump_data is None:
                    print(f"[{i}/{total_keys}] 键 {key.decode('utf-8', errors='ignore')} 无法导出，跳过")
                    skipped += 1
                    continue
                
                ttl = self.source_client.pttl(key)  # 获取毫秒级TTL
                
                if ttl == -2:  # 键不存在
                    print(f"[{i}/{total_keys}] 键 {key.decode('utf-8', errors='ignore')} 不存在，跳过")
                    skipped += 1
                    continue
                elif ttl == -1:  # 没有过期时间
                    ttl = 0
                
                # 如果需要覆盖，先删除目标键
                if overwrite and self.target_client.exists(key):
                    self.target_client.delete(key)
                
                # 使用RESTORE转移数据到目标Redis
                self.target_client.restore(key, ttl, dump_data)
                
                # 如果是移动模式，删除源数据
                if not copy_mode:
                    self.source_client.delete(key)
                
                transferred += 1
                action_text = "复制" if copy_mode else "移动"
                print(f"[{i}/{total_keys}] 成功{action_text}键: {key.decode('utf-8', errors='ignore')}")
                
            except Exception as e:
                failed += 1
                print(f"[{i}/{total_keys}] {mode_text}键失败 {key.decode('utf-8', errors='ignore')}: {e}")
        
        # 输出转移结果
        print("\n" + "="*50)
        print(f"{mode_text}完成!")
        print(f"总键数: {total_keys}")
        print(f"成功{mode_text}: {transferred}")
        print(f"跳过: {skipped}")
        print(f"失败: {failed}")
        
        # 验证结果
        source_size = self.get_db_size(self.source_client)
        target_size = self.get_db_size(self.target_client)
        print(f"\n验证结果:")
        if copy_mode:
            print(f"源数据库键数: {source_size} (数据保留)")
        else:
            print(f"源数据库键数: {source_size} (数据已移动)")
        print(f"目标数据库键数: {target_size}")

def parse_redis_config(config_str):
    """
    解析Redis配置字符串
    格式: host:port:password:db 或 host:port:db 或 host:port
    """
    parts = config_str.split(':')
    config = {
        'host': parts[0] if parts[0] else 'localhost',
        'port': int(parts[1]) if len(parts) > 1 and parts[1] else 6379,
        'password': None,
        'db': 0
    }
    
    if len(parts) == 3:
        # host:port:db 格式
        config['db'] = int(parts[2]) if parts[2] else 0
    elif len(parts) == 4:
        # host:port:password:db 格式
        config['password'] = parts[2] if parts[2] else None
        config['db'] = int(parts[3]) if parts[3] else 0
    
    return config

def main():
    """主函数"""
    if len(sys.argv) < 3:
        print("使用方法:")
        print("python redis_transfer.py <源Redis配置> <目标Redis配置> [选项]")
        print("")
        print("Redis配置格式:")
        print("  host:port:db                    # 无密码")
        print("  host:port:password:db           # 有密码")
        print("  localhost:6379:0                # 本地Redis DB0")
        print("  *************:6379:mypass:7    # 远程Redis DB7")
        print("")
        print("选项:")
        print("  --move        移动模式(删除源数据，默认为复制模式)")
        print("  --overwrite   覆盖已存在的键")
        print("")
        print("示例:")
        print("  # 从本地DB0复制到本地DB7")
        print("  python redis_transfer.py localhost:6379:0 localhost:6379:7")
        print("")
        print("  # 从本地DB0移动到远程DB7")
        print("  python redis_transfer.py localhost:6379:0 *************:6379:mypass:7 --move")
        print("")
        print("  # 从远程DB0复制到本地DB7，覆盖已存在的键")
        print("  python redis_transfer.py *************:6379:mypass:0 localhost:6379:7 --overwrite")
        sys.exit(1)
    
    # 解析配置
    source_config = parse_redis_config(sys.argv[1])
    target_config = parse_redis_config(sys.argv[2])
    
    # 解析选项
    copy_mode = '--move' not in sys.argv
    overwrite = '--overwrite' in sys.argv
    
    print("Redis数据转移工具")
    print("="*50)
    
    # 创建转移器
    transfer = RedisTransfer(source_config, target_config)
    
    # 测试连接
    if not transfer.test_connections():
        sys.exit(1)
    
    # 显示当前数据库状态
    source_size = transfer.get_db_size(transfer.source_client)
    target_size = transfer.get_db_size(transfer.target_client)
    
    print(f"\n当前状态:")
    print(f"源数据库键数: {source_size}")
    print(f"目标数据库键数: {target_size}")
    
    if source_size == 0:
        print("源数据库为空，无需转移")
        sys.exit(0)
    
    # 确认转移
    mode_text = "移动" if not copy_mode else "复制"
    action_text = "覆盖转移" if overwrite else mode_text
    
    print(f"\n转移配置:")
    print(f"源: {source_config['host']}:{source_config['port']} DB{source_config['db']}")
    print(f"目标: {target_config['host']}:{target_config['port']} DB{target_config['db']}")
    print(f"模式: {action_text}")
    
    confirm = input(f"\n确认要{action_text} {source_size} 个键吗? (y/N): ")
    if confirm.lower() != 'y':
        print("取消转移")
        sys.exit(0)
    
    # 开始转移
    start_time = time.time()
    transfer.transfer_data(overwrite=overwrite, copy_mode=copy_mode)
    end_time = time.time()
    
    print(f"\n总耗时: {end_time - start_time:.2f} 秒")

if __name__ == "__main__":
    main()
