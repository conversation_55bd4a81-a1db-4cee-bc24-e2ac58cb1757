#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地Redis不同DB之间的数据转移
从DB0复制到DB7
"""

import redis
import time

# ==================== 配置 ====================
REDIS_HOST = 'localhost'
REDIS_PORT = 6379
REDIS_PASSWORD = None  # 如果有密码，在这里填写

SOURCE_DB = 0  # 源数据库
TARGET_DB = 7  # 目标数据库

# 转移选项
COPY_MODE = True      # True=复制, False=移动
OVERWRITE = False     # True=覆盖已存在的键
SHOW_PROGRESS = True  # 显示详细进度

# ==================== 代码 ====================

def main():
    print("本地Redis数据库转移工具")
    print("="*40)
    print(f"从 DB{SOURCE_DB} -> DB{TARGET_DB}")
    print(f"模式: {'复制' if COPY_MODE else '移动'}")
    print(f"覆盖: {'是' if OVERWRITE else '否'}")
    print("-" * 40)
    
    # 创建Redis连接
    source_client = redis.Redis(
        host=REDIS_HOST,
        port=REDIS_PORT,
        password=REDIS_PASSWORD,
        db=SOURCE_DB,
        decode_responses=False
    )
    
    target_client = redis.Redis(
        host=REDIS_HOST,
        port=REDIS_PORT,
        password=REDIS_PASSWORD,
        db=TARGET_DB,
        decode_responses=False
    )
    
    # 测试连接
    try:
        source_client.ping()
        target_client.ping()
        print("✓ Redis连接成功")
    except Exception as e:
        print(f"✗ Redis连接失败: {e}")
        return
    
    # 获取数据库大小
    source_size = source_client.dbsize()
    target_size = target_client.dbsize()
    
    print(f"\n当前状态:")
    print(f"DB{SOURCE_DB} 键数: {source_size}")
    print(f"DB{TARGET_DB} 键数: {target_size}")
    
    if source_size == 0:
        print("源数据库为空")
        return
    
    # 确认执行
    action = "复制" if COPY_MODE else "移动"
    confirm = input(f"\n确认{action} {source_size} 个键? (y/N): ")
    if confirm.lower() != 'y':
        print("取消操作")
        return
    
    # 开始转移
    print(f"\n开始{action}数据...")
    start_time = time.time()
    
    # 获取所有键
    all_keys = []
    cursor = 0
    while True:
        cursor, keys = source_client.scan(cursor=cursor, count=100)
        all_keys.extend(keys)
        if cursor == 0:
            break
    
    # 转移数据
    transferred = 0
    skipped = 0
    failed = 0
    
    for i, key in enumerate(all_keys, 1):
        try:
            # 检查是否已存在
            if target_client.exists(key) and not OVERWRITE:
                if SHOW_PROGRESS:
                    print(f"[{i}/{source_size}] 跳过: {key.decode('utf-8', errors='ignore')}")
                skipped += 1
                continue
            
            # 使用DUMP/RESTORE
            dump_data = source_client.dump(key)
            if dump_data is None:
                skipped += 1
                continue
            
            ttl = source_client.pttl(key)
            if ttl == -2:
                skipped += 1
                continue
            elif ttl == -1:
                ttl = 0
            
            # 覆盖时先删除
            if OVERWRITE and target_client.exists(key):
                target_client.delete(key)
            
            # 恢复数据
            target_client.restore(key, ttl, dump_data)
            
            # 移动模式下删除源数据
            if not COPY_MODE:
                source_client.delete(key)
            
            transferred += 1
            
            if SHOW_PROGRESS:
                print(f"[{i}/{source_size}] {action}: {key.decode('utf-8', errors='ignore')}")
            elif i % 50 == 0:
                print(f"进度: {i}/{source_size}")
                
        except Exception as e:
            failed += 1
            if SHOW_PROGRESS:
                print(f"[{i}/{source_size}] 失败: {e}")
    
    end_time = time.time()
    
    # 结果统计
    print(f"\n{action}完成!")
    print(f"成功: {transferred}")
    print(f"跳过: {skipped}")
    print(f"失败: {failed}")
    print(f"耗时: {end_time - start_time:.2f}秒")
    
    # 最终状态
    final_source = source_client.dbsize()
    final_target = target_client.dbsize()
    print(f"\n最终状态:")
    print(f"DB{SOURCE_DB}: {final_source}")
    print(f"DB{TARGET_DB}: {final_target}")

if __name__ == "__main__":
    main()
