# Redis数据迁移命令参考

## 在Redis可视化工具中执行以下命令：

### 步骤1: 选择DB0，查看所有键
SELECT 0
KEYS *

### 步骤2: 批量移动所有键到DB7（在Redis工具的命令行中执行）
EVAL "local keys = redis.call('KEYS', '*'); local moved = 0; for i=1,#keys do local result = redis.call('MOVE', keys[i], 7); if result == 1 then moved = moved + 1 end end; return moved" 0

### 步骤3: 验证迁移结果
SELECT 0
DBSIZE

SELECT 7
DBSIZE

## 方法2: 使用DUMP和RESTORE命令（适用于跨Redis实例）
# 1. 导出单个键
SELECT 0
DUMP key_name

# 2. 导入到7号库
SELECT 7
RESTORE key_name 0 "dump_data"

## 方法3: 验证迁移结果
# 检查0号库剩余键数
SELECT 0
DBSIZE

# 检查7号库键数
SELECT 7
DBSIZE

## 方法4: 如果需要复制而不是移动
# 使用以下Lua脚本复制数据
EVAL "
local keys = redis.call('KEYS', '*')
local copied = 0
for i=1,#keys do
    local dump = redis.call('DUMP', keys[i])
    local ttl = redis.call('TTL', keys[i])
    if ttl == -1 then ttl = 0 end
    redis.call('SELECT', 7)
    redis.call('RESTORE', keys[i], ttl * 1000, dump)
    redis.call('SELECT', 0)
    copied = copied + 1
end
return copied
" 0
