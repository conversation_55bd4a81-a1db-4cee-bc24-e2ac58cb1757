#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import redis
import json
import sys
import time
from pathlib import Path

class RedisTransferWithConfig:
    def __init__(self, config_file='redis_config.json'):
        """从配置文件初始化"""
        self.load_config(config_file)
        self.setup_clients()
    
    def load_config(self, config_file):
        """加载配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            print(f"✓ 成功加载配置文件: {config_file}")
        except Exception as e:
            print(f"✗ 加载配置文件失败: {e}")
            sys.exit(1)
    
    def setup_clients(self):
        """设置Redis客户端"""
        source_config = self.config['source']
        target_config = self.config['target']
        
        self.source_client = redis.Redis(
            host=source_config['host'],
            port=source_config['port'],
            password=source_config.get('password'),
            db=source_config['db'],
            decode_responses=False
        )
        
        self.target_client = redis.Redis(
            host=target_config['host'],
            port=target_config['port'],
            password=target_config.get('password'),
            db=target_config['db'],
            decode_responses=False
        )
    
    def test_connections(self):
        """测试连接"""
        source_config = self.config['source']
        target_config = self.config['target']
        
        try:
            self.source_client.ping()
            print(f"✓ 源Redis连接成功: {source_config['host']}:{source_config['port']} DB{source_config['db']}")
        except Exception as e:
            print(f"✗ 源Redis连接失败: {e}")
            return False
            
        try:
            self.target_client.ping()
            print(f"✓ 目标Redis连接成功: {target_config['host']}:{target_config['port']} DB{target_config['db']}")
        except Exception as e:
            print(f"✗ 目标Redis连接失败: {e}")
            return False
            
        return True
    
    def transfer_data(self):
        """执行数据转移"""
        options = self.config['options']
        copy_mode = options.get('copy_mode', True)
        overwrite = options.get('overwrite', False)
        batch_size = options.get('batch_size', 100)
        
        mode_text = "复制" if copy_mode else "移动"
        print(f"\n开始{mode_text}数据...")
        
        # 获取所有键
        all_keys = []
        cursor = 0
        
        while True:
            cursor, keys = self.source_client.scan(cursor=cursor, count=batch_size)
            all_keys.extend(keys)
            if cursor == 0:
                break
        
        total_keys = len(all_keys)
        print(f"找到 {total_keys} 个键需要{mode_text}")
        
        if total_keys == 0:
            print("源数据库为空，无需转移")
            return
        
        # 转移数据
        transferred = 0
        failed = 0
        skipped = 0
        
        for i, key in enumerate(all_keys, 1):
            try:
                if self.target_client.exists(key) and not overwrite:
                    print(f"[{i}/{total_keys}] 跳过: {key.decode('utf-8', errors='ignore')}")
                    skipped += 1
                    continue
                
                # DUMP & RESTORE
                dump_data = self.source_client.dump(key)
                if dump_data is None:
                    skipped += 1
                    continue
                
                ttl = self.source_client.pttl(key)
                if ttl == -2:
                    skipped += 1
                    continue
                elif ttl == -1:
                    ttl = 0
                
                if overwrite and self.target_client.exists(key):
                    self.target_client.delete(key)
                
                self.target_client.restore(key, ttl, dump_data)
                
                if not copy_mode:
                    self.source_client.delete(key)
                
                transferred += 1
                if i % 100 == 0 or i == total_keys:
                    print(f"[{i}/{total_keys}] 进度: {i/total_keys*100:.1f}%")
                
            except Exception as e:
                failed += 1
                print(f"[{i}/{total_keys}] 失败: {key.decode('utf-8', errors='ignore')} - {e}")
        
        # 结果统计
        print(f"\n{mode_text}完成!")
        print(f"成功: {transferred}, 跳过: {skipped}, 失败: {failed}")
        
        # 验证
        source_size = self.source_client.dbsize()
        target_size = self.target_client.dbsize()
        print(f"源数据库键数: {source_size}")
        print(f"目标数据库键数: {target_size}")

def create_sample_configs():
    """创建示例配置文件"""
    
    # 同Redis不同DB配置
    same_redis_config = {
        "source": {
            "host": "localhost",
            "port": 6379,
            "password": None,
            "db": 0,
            "description": "本地Redis DB0"
        },
        "target": {
            "host": "localhost",
            "port": 6379,
            "password": None,
            "db": 7,
            "description": "本地Redis DB7"
        },
        "options": {
            "copy_mode": True,
            "overwrite": False,
            "batch_size": 100
        }
    }
    
    # 不同Redis配置
    different_redis_config = {
        "source": {
            "host": "*************",
            "port": 6379,
            "password": "source_password",
            "db": 0,
            "description": "远程源Redis"
        },
        "target": {
            "host": "*************",
            "port": 6379,
            "password": "target_password",
            "db": 0,
            "description": "远程目标Redis"
        },
        "options": {
            "copy_mode": False,
            "overwrite": True,
            "batch_size": 200
        }
    }
    
    with open('redis_same_db_config.json', 'w', encoding='utf-8') as f:
        json.dump(same_redis_config, f, indent=2, ensure_ascii=False)
    
    with open('redis_different_config.json', 'w', encoding='utf-8') as f:
        json.dump(different_redis_config, f, indent=2, ensure_ascii=False)
    
    print("✓ 创建示例配置文件:")
    print("  - redis_same_db_config.json (同Redis不同DB)")
    print("  - redis_different_config.json (不同Redis)")

def main():
    if len(sys.argv) < 2:
        print("Redis数据转移工具 (配置文件版)")
        print("="*40)
        print("使用方法:")
        print("  python redis_transfer_config.py <配置文件>")
        print("  python redis_transfer_config.py --create-samples")
        print("")
        print("示例:")
        print("  python redis_transfer_config.py redis_config.json")
        sys.exit(1)
    
    if sys.argv[1] == '--create-samples':
        create_sample_configs()
        return
    
    config_file = sys.argv[1]
    if not Path(config_file).exists():
        print(f"✗ 配置文件不存在: {config_file}")
        sys.exit(1)
    
    # 执行转移
    transfer = RedisTransferWithConfig(config_file)
    
    if not transfer.test_connections():
        sys.exit(1)
    
    # 显示配置信息
    print(f"\n配置信息:")
    print(f"源: {transfer.config['source']['description']}")
    print(f"目标: {transfer.config['target']['description']}")
    print(f"模式: {'复制' if transfer.config['options']['copy_mode'] else '移动'}")
    print(f"覆盖: {'是' if transfer.config['options']['overwrite'] else '否'}")
    
    confirm = input(f"\n确认执行转移? (y/N): ")
    if confirm.lower() != 'y':
        print("取消转移")
        sys.exit(0)
    
    start_time = time.time()
    transfer.transfer_data()
    end_time = time.time()
    
    print(f"\n总耗时: {end_time - start_time:.2f} 秒")

if __name__ == "__main__":
    main()
