@echo off
echo Redis数据迁移工具
echo ==================
echo.
echo 由于redis-cli不可用，请按以下步骤操作：
echo.
echo 1. 打开你的Redis可视化工具（如RedisDesktopManager、Another Redis Desktop Manager等）
echo.
echo 2. 连接到你的Redis服务器
echo.
echo 3. 选择DB0数据库
echo.
echo 4. 在命令行界面执行以下命令：
echo.
echo    查看DB0的所有键：
echo    KEYS *
echo.
echo 5. 执行批量迁移命令（复制下面这行到Redis工具中）：
echo.
echo EVAL "local keys = redis.call('KEYS', '*'); local moved = 0; for i=1,#keys do local result = redis.call('MOVE', keys[i], 7); if result == 1 then moved = moved + 1 end end; return moved" 0
echo.
echo 6. 验证迁移结果：
echo    SELECT 0
echo    DBSIZE
echo    SELECT 7  
echo    DBSIZE
echo.
echo 注意：MOVE命令会将键从源库移动到目标库（不是复制）
echo 如果目标库已存在同名键，移动会失败
echo.
pause
