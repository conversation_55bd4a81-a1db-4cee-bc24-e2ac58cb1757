#!/bin/bash

# Redis数据库迁移脚本：从0号库迁移到7号库
# 使用方法: ./redis_migrate_0_to_7.sh [redis-host] [redis-port] [password]

REDIS_HOST=${1:-localhost}
REDIS_PORT=${2:-6379}
REDIS_PASSWORD=${3:-""}

echo "开始从Redis 0号库迁移数据到7号库..."
echo "Redis服务器: $REDIS_HOST:$REDIS_PORT"

# 构建redis-cli命令
if [ -n "$REDIS_PASSWORD" ]; then
    REDIS_CLI="redis-cli -h $REDIS_HOST -p $REDIS_PORT -a $REDIS_PASSWORD"
else
    REDIS_CLI="redis-cli -h $REDIS_HOST -p $REDIS_PORT"
fi

# 选择0号库并获取所有键
echo "正在获取0号库的所有键..."
KEYS=$($REDIS_CLI -n 0 KEYS "*")

if [ -z "$KEYS" ]; then
    echo "0号库中没有数据需要迁移"
    exit 0
fi

# 统计键的数量
KEY_COUNT=$(echo "$KEYS" | wc -l)
echo "找到 $KEY_COUNT 个键需要迁移"

# 迁移每个键
MIGRATED=0
FAILED=0

for key in $KEYS; do
    echo -n "迁移键: $key ... "
    
    # 使用MOVE命令将键从0号库移动到7号库
    result=$($REDIS_CLI -n 0 MOVE "$key" 7)
    
    if [ "$result" = "1" ]; then
        echo "成功"
        ((MIGRATED++))
    else
        echo "失败 (可能7号库中已存在同名键)"
        ((FAILED++))
    fi
done

echo ""
echo "迁移完成!"
echo "成功迁移: $MIGRATED 个键"
echo "失败: $FAILED 个键"

# 验证迁移结果
echo ""
echo "验证迁移结果:"
DB0_COUNT=$($REDIS_CLI -n 0 DBSIZE)
DB7_COUNT=$($REDIS_CLI -n 7 DBSIZE)
echo "0号库剩余键数: $DB0_COUNT"
echo "7号库键数: $DB7_COUNT"
