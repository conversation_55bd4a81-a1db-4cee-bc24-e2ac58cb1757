#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import redis
import sys
import time
from typing import Optional

class RedisCopier:
    def __init__(self, host='localhost', port=6379, password=None):
        """
        初始化Redis连接
        """
        self.host = host
        self.port = port
        self.password = password
        
        # 创建Redis连接
        self.redis_client = redis.Redis(
            host=host,
            port=port,
            password=password,
            decode_responses=True
        )
        
    def test_connection(self):
        """测试Redis连接"""
        try:
            self.redis_client.ping()
            print(f"✓ 成功连接到Redis服务器 {self.host}:{self.port}")
            return True
        except Exception as e:
            print(f"✗ 连接Redis失败: {e}")
            return False
    
    def get_db_size(self, db_num):
        """获取指定数据库的键数量"""
        try:
            # 切换到指定数据库
            temp_client = redis.Redis(
                host=self.host,
                port=self.port,
                password=self.password,
                db=db_num,
                decode_responses=True
            )
            return temp_client.dbsize()
        except Exception as e:
            print(f"获取数据库{db_num}大小失败: {e}")
            return 0
    
    def copy_data(self, source_db=0, target_db=7, batch_size=100, overwrite=False):
        """
        复制数据从源数据库到目标数据库
        """
        print(f"开始复制数据: DB{source_db} -> DB{target_db}")
        if overwrite:
            print("模式: 覆盖已存在的键")
        else:
            print("模式: 跳过已存在的键")
        
        # 创建源数据库和目标数据库的连接
        source_client = redis.Redis(
            host=self.host,
            port=self.port,
            password=self.password,
            db=source_db,
            decode_responses=False  # 保持原始数据格式
        )
        
        target_client = redis.Redis(
            host=self.host,
            port=self.port,
            password=self.password,
            db=target_db,
            decode_responses=False
        )
        
        # 获取所有键
        print("正在扫描源数据库的所有键...")
        all_keys = []
        cursor = 0
        
        while True:
            cursor, keys = source_client.scan(cursor=cursor, count=batch_size)
            all_keys.extend(keys)
            if cursor == 0:
                break
        
        total_keys = len(all_keys)
        print(f"找到 {total_keys} 个键需要复制")
        
        if total_keys == 0:
            print("源数据库为空，无需复制")
            return
        
        # 开始复制
        copied = 0
        failed = 0
        skipped = 0
        
        for i, key in enumerate(all_keys, 1):
            try:
                # 检查目标数据库是否已存在该键
                if target_client.exists(key) and not overwrite:
                    print(f"[{i}/{total_keys}] 跳过键 {key.decode('utf-8', errors='ignore')} (目标库已存在)")
                    skipped += 1
                    continue
                
                # 使用DUMP和RESTORE进行完整复制（保持所有数据类型和TTL）
                dump_data = source_client.dump(key)
                ttl = source_client.pttl(key)  # 获取毫秒级TTL
                
                if ttl == -1:  # 没有过期时间
                    ttl = 0
                elif ttl == -2:  # 键不存在
                    print(f"[{i}/{total_keys}] 键 {key.decode('utf-8', errors='ignore')} 不存在，跳过")
                    skipped += 1
                    continue
                
                # 如果需要覆盖，先删除目标键
                if overwrite and target_client.exists(key):
                    target_client.delete(key)
                
                # 使用RESTORE复制数据
                target_client.restore(key, ttl, dump_data)
                
                copied += 1
                print(f"[{i}/{total_keys}] 成功复制键: {key.decode('utf-8', errors='ignore')}")
                
            except Exception as e:
                failed += 1
                print(f"[{i}/{total_keys}] 复制键失败 {key.decode('utf-8', errors='ignore')}: {e}")
        
        # 输出复制结果
        print("\n" + "="*50)
        print("复制完成!")
        print(f"总键数: {total_keys}")
        print(f"成功复制: {copied}")
        print(f"跳过: {skipped}")
        print(f"失败: {failed}")
        
        # 验证结果
        source_size = self.get_db_size(source_db)
        target_size = self.get_db_size(target_db)
        print(f"\n验证结果:")
        print(f"源数据库 DB{source_db} 键数: {source_size} (数据保留)")
        print(f"目标数据库 DB{target_db} 键数: {target_size}")

def main():
    """主函数"""
    # 解析命令行参数
    if len(sys.argv) < 2:
        print("使用方法:")
        print("python redis_copy.py <host> [port] [password] [--overwrite]")
        print("例如: python redis_copy.py localhost 6379 mypassword")
        print("     python redis_copy.py localhost 6379 mypassword --overwrite")
        sys.exit(1)
    
    host = sys.argv[1]
    port = int(sys.argv[2]) if len(sys.argv) > 2 else 6379
    password = sys.argv[3] if len(sys.argv) > 3 and not sys.argv[3].startswith('--') else None
    overwrite = '--overwrite' in sys.argv
    
    # 创建复制器
    copier = RedisCopier(host=host, port=port, password=password)
    
    # 测试连接
    if not copier.test_connection():
        sys.exit(1)
    
    # 显示当前数据库状态
    db0_size = copier.get_db_size(0)
    db7_size = copier.get_db_size(7)
    
    print(f"\n当前状态:")
    print(f"DB0 键数: {db0_size}")
    print(f"DB7 键数: {db7_size}")
    
    if db0_size == 0:
        print("DB0 为空，无需复制")
        sys.exit(0)
    
    # 确认复制
    action = "覆盖复制" if overwrite else "复制"
    confirm = input(f"\n确认要将 {db0_size} 个键从 DB0 {action}到 DB7 吗? (y/N): ")
    if confirm.lower() != 'y':
        print("取消复制")
        sys.exit(0)
    
    # 开始复制
    start_time = time.time()
    copier.copy_data(source_db=0, target_db=7, overwrite=overwrite)
    end_time = time.time()
    
    print(f"\n总耗时: {end_time - start_time:.2f} 秒")

if __name__ == "__main__":
    main()
