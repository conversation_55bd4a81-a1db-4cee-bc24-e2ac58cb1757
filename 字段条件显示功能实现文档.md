# 字段条件显示功能实现文档

## 功能概述

实现基于某个字段选择值动态控制其他字段显示/隐藏的功能，支持字段间联动清空逻辑。

### 核心特性
- 🎯 基于fieldId精确控制字段显示
- 🔄 支持字段间联动清空
- 📱 适用于表单和编辑页面
- 🎨 支持自定义选项添加
- 📋 支持展示页面正确回显

## 实现案例：毕业学校字段控制

### 业务需求
- 毕业学校字段（fieldId: 602）默认隐藏
- 毕业小学字段（fieldId: 600）添加"其他"选项（值为999999）
- 选择"其他"时：显示毕业学校，隐藏毕业班级（fieldId: 601）
- 选择具体学校时：显示毕业班级，隐藏毕业学校

### 显示逻辑表
| 毕业小学选择 | 毕业学校字段 | 毕业班级字段 |
|------------|------------|------------|
| 具体学校    | 隐藏       | 显示       |
| 其他       | 显示       | 隐藏       |

## 核心实现代码

### 1. 添加特殊选项到下拉列表
**文件：** `src/components/Form/NormalSelect.vue`

```javascript
primarySchoolList() {
    // API查询学校列表
    deptPageList(params).then(res => {
        res.records.forEach(v => {
            v.val = v.deptName
        })
        this.dataSource = res.records
        
        // 为特定字段添加"其他"选项
        if (this.itemConfig.fieldId == 600) {
            this.dataSource.push({
                id: '999999',
                val: '其他',
                deptName: '其他'
            })
        }
    })
}
```

### 2. 表单条件显示逻辑
**文件：** `src/views/adForm/index.vue` 和 `src/views/adForm/edit.vue`

```html
<template v-for="fi, fidx in item._normalItem">
  <!-- 条件显示字段：毕业学校 -->
  <el-form-item v-if="fi.fieldId != 602 || (fi.fieldId == 602 && form['600'] == '999999')"
                :prop="`${ fi.fieldId }`" :label="fi.fieldName" class="f-g-d-item normal-item">
    <normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange"
                   v-if="fi.inputItemCode == 2 || fi.inputItemCode == 5 || fi.inputItemCode == 6">
    </normal-select>
  </el-form-item>
  
  <!-- 条件隐藏字段：毕业班级 -->
  <el-form-item v-else-if="fi.fieldId == 601 && form['600'] != '999999'"
                :prop="`${ fi.fieldId }`" :label="fi.fieldName" class="f-g-d-item normal-item">
    <normal-select :ref="fi.fieldId" :item-config.sync="fi" @value-change="selectValueChange">
    </normal-select>
  </el-form-item>
  
  <!-- 其他字段正常显示 -->
  <el-form-item v-else-if="fi.fieldId != 601 && fi.fieldId != 602"
                :prop="`${ fi.fieldId }`" :label="fi.fieldName" class="f-g-d-item normal-item">
    <!-- 字段内容 -->
  </el-form-item>
</template>
```

### 3. 字段联动处理
**文件：** `src/mixins/loopFn.js`

```javascript
selectValueChange(v) {
    this.changeVal(v.id, v.val)
    
    // 毕业小学字段联动处理
    if (v.id == 600) {
        if (v.val == '999999') {
            // 选择"其他"时清空毕业班级
            this.changeVal(601, '')
        } else {
            // 选择具体学校时清空毕业学校
            this.changeVal(602, '')
        }
    }
}
```

### 4. 展示组件处理
**文件：** `src/components/Exhibition/NormalSelectEx.vue`

```javascript
primarySchoolList() {
    deptPageList(params).then(res => {
        res.records.forEach(v => {
            if (v.id == this.fieldVal) {
                this.modelData = v.deptName
            }
        })
        
        // 处理特殊值显示
        if (this.itemConfig.fieldId == 600 && this.fieldVal == '999999') {
            this.modelData = '其他'
        }
    })
}
```

## 通用实现模板

### 模板参数说明
- `[TRIGGER_FIELD_ID]`: 触发字段ID
- `[SHOW_FIELD_ID]`: 条件显示字段ID  
- `[HIDE_FIELD_ID]`: 条件隐藏字段ID
- `[SPECIAL_VALUE]`: 特殊选项值
- `[DISPLAY_TEXT]`: 显示文本

### 1. 添加特殊选项模板
```javascript
if (this.itemConfig.fieldId == [TRIGGER_FIELD_ID]) {
    this.dataSource.push({
        id: '[SPECIAL_VALUE]',
        val: '[DISPLAY_TEXT]',
        deptName: '[DISPLAY_TEXT]'
    })
}
```

### 2. 条件显示模板
```html
<!-- 条件显示字段 -->
<el-form-item v-if="fi.fieldId != [SHOW_FIELD_ID] || (fi.fieldId == [SHOW_FIELD_ID] && form['[TRIGGER_FIELD_ID]'] == '[SPECIAL_VALUE]')">
  <!-- 字段内容 -->
</el-form-item>

<!-- 条件隐藏字段 -->
<el-form-item v-else-if="fi.fieldId == [HIDE_FIELD_ID] && form['[TRIGGER_FIELD_ID]'] != '[SPECIAL_VALUE]'">
  <!-- 字段内容 -->
</el-form-item>

<!-- 其他字段 -->
<el-form-item v-else-if="fi.fieldId != [SHOW_FIELD_ID] && fi.fieldId != [HIDE_FIELD_ID]">
  <!-- 字段内容 -->
</el-form-item>
```

### 3. 联动处理模板
```javascript
if (v.id == [TRIGGER_FIELD_ID]) {
    if (v.val == '[SPECIAL_VALUE]') {
        this.changeVal([HIDE_FIELD_ID], '')
    } else {
        this.changeVal([SHOW_FIELD_ID], '')
    }
}
```

### 4. 展示处理模板
```javascript
if (this.itemConfig.fieldId == [TRIGGER_FIELD_ID] && this.fieldVal == '[SPECIAL_VALUE]') {
    this.modelData = '[DISPLAY_TEXT]'
}
```

## 应用示例

### 示例1：居住证类型控制
```javascript
// 居住证类型(700) 控制 派出所字段(143)
// 选择"居住凭证"时显示派出所字段

// 1. 条件显示
v-if="fi.fieldId != 143 || (fi.fieldId == 143 && form['700'] == 'residence_permit')"

// 2. 联动处理
if (v.id == 700 && v.val != 'residence_permit') {
    this.changeVal(143, '')
}
```

### 示例2：学历类型控制
```javascript
// 学历类型(500) 控制 自定义学历字段(501)
// 选择"其他"时显示自定义学历输入框

// 1. 添加选项
if (this.itemConfig.fieldId == 500) {
    this.dataSource.push({id: 'other', val: '其他'})
}

// 2. 条件显示
v-if="fi.fieldId != 501 || (fi.fieldId == 501 && form['500'] == 'other')"
```

## 最佳实践

### ✅ 推荐做法
1. **使用fieldId标识**：避免字段名重复问题
2. **逻辑清晰分离**：条件显示、联动处理分别实现
3. **保持值一致性**：特殊值在所有地方统一
4. **及时清空关联**：切换时清空相关字段值

### ❌ 避免问题
1. **复杂嵌套判断**：保持条件逻辑简单明了
2. **忘记展示处理**：确保详情页正确显示特殊值
3. **验证规则冲突**：隐藏字段要调整验证规则
4. **性能问题**：避免在条件判断中进行复杂计算

## 扩展功能

### 多级联动
```javascript
// A控制B，B控制C的联动
if (v.id == fieldA) {
    this.changeVal(fieldB, '')
    this.changeVal(fieldC, '')
} else if (v.id == fieldB) {
    this.changeVal(fieldC, '')
}
```

### 多条件控制
```html
<!-- A和B同时控制C -->
<el-form-item v-if="form['fieldA'] == 'valueA' && form['fieldB'] == 'valueB'">
  <!-- 字段C -->
</el-form-item>
```

## 总结

此实现方案提供了完整的字段条件显示解决方案，具有良好的通用性和扩展性。通过模板化的代码结构，可以快速应用到各种业务场景中，提高开发效率和代码质量。

### 核心优势
- 🚀 **快速实现**：模板化代码，复制即用
- 🔧 **易于维护**：逻辑清晰，结构简单
- 📈 **高扩展性**：支持复杂联动场景
- 🎯 **精确控制**：基于fieldId的精确字段控制
