# 批量修复导出按钮 Loading 状态指南

## 需要修复的文件列表

以下是包含导出功能但缺少 loading 状态的文件：

### 1. 数据验证相关
- `src/views/unitedDept/property.vue` - 房管信息导出
- `src/views/dataVerify/propertyZhuJian.vue` - 房管信息导出
- `src/views/unitedDept/policeResidencePermit.vue` - 居住证信息导出

### 2. 报名信息导出
- `src/components/biaoZhunBanChuZhongStudentList/index.vue` - 导出报名信息、下载入户调查单
- `src/components/biaoZhunBanXiaoXueStudentList/index.vue` - 导出报名信息、下载入户调查单
- `src/views/enrollment/primaryQW/index.vue` - 导出报名信息
- `src/views/delaySchool/index.vue` - 导出延缓入学信息
- `src/views/outsideSchool/index.vue` - 导出外地上学报名信息
- `src/views/enrollment/graduate/index.vue` - 导出毕业生信息

### 3. 县区特定功能
- `src/views/counties/feiXiang/enrollment/biYeXiaoXueShenHe/index.vue`
- `src/views/counties/linZhang-d/enrollment/junior/index.vue`
- `src/views/counties/qiuxian/enrollment/junior/index.vue`
- `src/views/counties/chengAn/dianLiJuShenHe/index.vue`
- `src/views/counties/quZhou/shiChangJianDuGuanLiJu/index.vue`
- `src/views/counties/feiXiang/enrollment/junior1/index.vue`
- `src/views/counties/chengAn/xingZhengShenHe/index.vue`
- `src/views/counties/yongNian/enrollment/junior/index.vue`

### 4. 派位结果导出
- `src/views/dispatch/yongNian/result.vue`
- `src/views/dispatch/qiuXian/luQuJieGuo.vue`
- `src/views/dispatch/linZhang/luQuJieGuoSchool.vue`

### 5. 民办学校相关
- `src/components/privatePrimaryEnroll/list.vue`
- `src/components/privateJuniorEnroll/list.vue`
- `src/views/privateEnrollment/junior/list.vue`

## 修复模式

### 1. 按钮修复模式
```html
<!-- 修复前 -->
<el-button @click="exportList">导出</el-button>

<!-- 修复后 -->
<el-button @click="exportList" :loading="exportLoading">导出</el-button>
```

### 2. 数据属性修复模式
```javascript
// 在 data() 的 return 中添加
exportLoading: false,
```

### 3. 方法修复模式
```javascript
// 修复前
exportList() {
  this.$download(url, params, type, filename).then((res) => {
    this.$message.success("导出成功");
  });
}

// 修复后
exportList() {
  // 防止重复点击
  if (this.exportLoading) {
    return;
  }

  this.exportLoading = true;
  this.$download(url, params, type, filename).then((res) => {
    this.exportLoading = false;
    this.$message.success("导出成功");
  }).catch((error) => {
    this.exportLoading = false;
    this.$message.error("导出失败");
    console.error("导出错误:", error);
  });
}
```

## 常见导出方法名
- `exportList()` - 通用导出列表
- `exportEnrollInfo()` - 导出报名信息
- `exportData()` - 导出数据
- `exportHouseholdSurvey()` - 导出入户调查单
- `exportRes()` - 导出结果

## 常见导出按钮文本
- "导出"
- "导出信息"
- "导出报名信息"
- "下载"
- "导出数据"

## 修复优先级

### 高优先级（用户使用频繁）
1. `src/views/enrollment/primaryQW/index.vue`
2. `src/views/enrollment/graduate/index.vue`
3. `src/components/biaoZhunBanChuZhongStudentList/index.vue`
4. `src/components/biaoZhunBanXiaoXueStudentList/index.vue`

### 中优先级（特定功能）
1. `src/views/delaySchool/index.vue`
2. `src/views/outsideSchool/index.vue`
3. `src/views/unitedDept/property.vue`
4. `src/views/dataVerify/propertyZhuJian.vue`

### 低优先级（县区特定）
1. 各县区特定的报名页面
2. 派位结果导出页面
3. 民办学校相关页面

## 注意事项

1. **保持一致性**：所有导出按钮都应该使用 `exportLoading` 作为 loading 状态变量名
2. **错误处理**：确保在 catch 块中重置 loading 状态
3. **用户体验**：添加适当的错误提示信息
4. **防重复点击**：在方法开始时检查 loading 状态
5. **调试信息**：在 catch 块中添加 console.error 便于调试

## 测试验证

修复完成后，需要验证：
1. 点击导出按钮后，按钮显示 loading 状态
2. 导出成功后，loading 状态消失，显示成功消息
3. 导出失败后，loading 状态消失，显示失败消息
4. 在 loading 期间，无法重复点击按钮
