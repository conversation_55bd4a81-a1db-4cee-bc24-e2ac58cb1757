<template>
  <div>
    <div class="search-form sd-m-b-10">
      <div class="search-form_left">
        <el-button
          size="small"
          icon="el-icon-plus"
          type="primary"
          @click="addSchool"
          >添加</el-button
        >
        <el-button
          size="small"
          type="success"
          icon="el-icon-upload2"
          @click="importBatch"
          >学校批量导入</el-button
        >
        <el-button
          size="small"
          type="warning"
          icon="el-icon-upload2"
          @click="importBatchUser"
          >账号批量导入</el-button
        >
        <el-button
          type="primary"
          icon="el-icon-download"
          size="small"
          @click="exportListUser"
          :loading="exportLoading"
          >账号导出</el-button
        >
        <el-button
          v-if="prefixDeptCode=='130224' && $store.getters.role === 'COUNTY_ADMIN'"
          size="small"
          type="warning"
          icon="el-icon-picture"
          @click="exportQrcodeList"
          :loading="qrcodeExportLoading"
          >导出学校二维码</el-button
        >
      </div>
      <div class="search-form_right">
        <el-form :model="search" :inline="true">
          <el-form-item prop="nature" v-if="!isFive">
            <el-select
              size="small"
              v-model="search.nature"
              placeholder="办学性质"
              class="sd-w-200"
              clearable
            >
              <el-option label="乡镇" value="1"></el-option>
              <el-option label="城区" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="nature">
            <el-select
                size="small"
                v-model="search.type"
                placeholder="学校类型"
                class="sd-w-200"
                clearable
            >
              <el-option label="公办" value="1"></el-option>
              <el-option label="民办 " value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="period" v-if="!isFive">
            <el-select
              size="small"
              v-model="search.period"
              placeholder="学段"
              class="sd-w-200"
              clearable
            >
              <el-option label="幼儿园" value="1"></el-option>
              <el-option label="小学" value="2"></el-option>
              <el-option label="初中" value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="keywords">
            <el-input
              size="small"
              v-model.trim="search.keywords"
              placeholder="学校名称"
              class="sd-w-200"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button
              size="small"
              type="primary"
              icon="el-icon-search"
              @click="searchSubmit"
            ></el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <el-table :data="tableData.records" border stripe style="width: 100%">
      <el-table-column
        align="center"
        label="序号"
        width="60"
        type="index"
        fixed="left"
      ></el-table-column>
      <el-table-column
        align="center"
        label="学校名称"
        prop="deptName"
      ></el-table-column>
      <el-table-column
        align="center"
        label="学校性质"
        width="80"
        prop="nature"
      >
        <template slot-scope="scope">
          <span class="iconfont icon-town" v-if="$store.getters.deptCode === '130209'">{{ ["", "乡镇", "主城区","新城区","工业区"][scope.row.nature] }}</span>
          <span class="iconfont icon-town" v-else>{{ ["", "乡镇", "城区"][scope.row.nature] }}</span>
        </template>
      </el-table-column>
      <el-table-column
          align="center"
          label="学校类型"
          width="80"
          prop="nature"
      >
        <template slot-scope="scope">
          <span class="iconfont icon-town">{{ ["", "公办", "民办"][scope.row.type] }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="学段" width="70" prop="period">
        <template slot-scope="scope">
          {{ ["", "幼儿园", "小学", "初中"][scope.row.period] }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="学校地址"
        prop="address"
      ></el-table-column>
      <el-table-column
          align="center"
          label="学校标识"
          prop="schoolBiaoShi"
          v-if="prefixDeptCode== 130207"
      ></el-table-column>
      <el-table-column
        align="center"
        label="联系人"
        width="70"
        prop="master"
      ></el-table-column>
      <el-table-column
        align="center"
        label="联系电话"
        width="150"
        prop="mobile"
      ></el-table-column>
      <el-table-column
        align="center"
        label="招生类别"
        prop="enrollType"
       width="300"
      >
        <template  slot-scope="{ row }"   >
           {{switchEnrollType(row.enrollType)}}

        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="招生计划数"
        width="100"
        prop="planNum"
      ></el-table-column>
      <el-table-column
        align="center"
        label="学校状态"
        width="100"
        prop="status"
      >
        <template slot-scope="{ row }">
          <el-link type="info" :underline="false" v-show="row.status == 0"
            >禁用</el-link
          >
          <el-link type="success" :underline="false" v-show="row.status == 1"
            >启用</el-link
          >
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="300px" fixed="right">
        <template slot-scope="{ row }">
          <el-link
            icon="el-icon-more-outline"
            type="primary"
            :underline="false"
            style="margin-right: 10px"
            @click="acctManage(row)"
            >账号管理</el-link
          >
          <el-link
            icon="el-icon-close"
            type="info"
            :underline="false"
            style="margin-right: 10px"
            v-if="row.status == 1"
            @click="disable(row)"
            >禁用</el-link
          >
          <el-link
            icon="el-icon-check"
            type="success"
            :underline="false"
            style="margin-right: 10px"
            v-else
            @click="enable(row)"
            >启用</el-link
          >
          <el-link
            icon="el-icon-edit"
            type="warning"
            :underline="false"
            style="margin-right: 10px"
            @click="edit(row)"
            >编辑</el-link
          >
          <el-link
            icon="el-icon-delete"
            type="danger"
            :underline="false"
            style="margin-right: 10px"
            @click="del(row)"
            >删除</el-link
          >
          <el-link
              v-if="prefixDeptCode=='130426'"
              icon="el-icon-s-tools"
              type="success"
              :underline="false"
              style="margin-right: 10px"
              @click="settingTime(row)"
          >设置报名时间</el-link
          >
          <el-link
              v-if="prefixDeptCode=='130224' && $store.getters.role === 'COUNTY_ADMIN'"
              type="primary"
              :underline="false"
              style="margin-right: 10px"
              @click="manageQrcode(row)"
          >学校二维码管理</el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="page-container" v-if="total > 0">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="search.pageNumber"
        layout="total, prev, pager, next, sizes"
        :page-sizes="$pageSizes"
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 新增，编辑 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="modal.addOrEdit"
      center
      width="1000px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="addForm"
        ref="addForm"
        label-width="180px"
        :rules="rules"
      >
        <el-form-item prop="deptName" label="学校名称">
          <el-input
            size="small"
            v-model.trim="addForm.deptName"
            placeholder="请输入学校名称"
            style="width: 400px"
          ></el-input>
        </el-form-item>
        <el-form-item prop="address" label="学校地址">
          <el-input
            size="small"
            v-model.trim="addForm.address"
            placeholder="请输入学校地址"
            style="width: 400px"
          ></el-input>
        </el-form-item>
        <el-form-item prop="schoolBiaoShi" label="学校标识" v-if="prefixDeptCode==130207">
          <el-input
              size="small"
              v-model.trim="addForm.schoolBiaoShi"
              placeholder="请输入学校标识"
              style="width: 400px"
          ></el-input>
        </el-form-item>
        <el-form-item prop="nature" label="学校性质" v-if="!isFive">
          <el-select
            size="small"
            v-model="addForm.nature"
            placeholder="学校性质"
            style="width: 260px"
          >
            <el-option label="乡镇" value="1"></el-option>
            <el-option :label="$store.getters.deptCode === '130209'?'主城区':'城区'" value="2"></el-option>

            <el-option label="新城区" value="3" v-if="$store.getters.deptCode === '130209'"></el-option>
            <el-option label="工业区" value="4" v-if="$store.getters.deptCode === '130209'"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="type" label="学校类型">
          <el-select
              size="small"
              v-model="addForm.type"
              placeholder="学校类型"
              style="width: 260px"
          >
            <el-option label="公办" value="1"></el-option>
            <el-option label="民办" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="period" label="学段" v-if="!isFive">
          <el-select
            size="small"
            v-model="addForm.period"
            placeholder="学段"
            style="width: 260px"
          >
            <el-option label="幼儿园" value="1"></el-option>
            <el-option label="小学" value="2"></el-option>
            <el-option label="初中" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="planNum" label="招生计划数" v-if="prefixDeptCode!='130426'">
          <el-input
            size="small"
            v-model.number.trim="addForm.planNum"
            placeholder="请输入招生计划数"
            style="width: 260px"
          ></el-input>
        </el-form-item>
<!--        涉县独有功能-->
        <el-form-item prop="hukouNum" label="户口-招生计划数" v-if="prefixDeptCode=='130426'">
          <el-input
              size="small"
              v-model.number.trim="addForm.hukouNum"
              placeholder="请输入户口-招生计划数"
              style="width: 260px"
          ></el-input>
        </el-form-item>
        <el-form-item prop="houseNum" label="房产-招生计划数" v-if="prefixDeptCode=='130426'">
          <el-input
              size="small"
              v-model.number.trim="addForm.houseNum"
              placeholder="请输入房产-招生计划数"
              style="width: 260px"
          ></el-input>
        </el-form-item>
        <el-form-item prop="businessNum" label="经商务工-招生计划数" v-if="prefixDeptCode=='130426'">
          <el-input
              size="small"
              v-model.number.trim="addForm.businessNum"
              placeholder="请输入经商务工-招生计划数"
              style="width: 260px"
          ></el-input>
        </el-form-item>
        <el-form-item prop="enrollTypeList" label="招生类别" v-if="prefixDeptCode!='130426'">
          <el-checkbox-group v-model="addForm.enrollTypeList">
            <el-checkbox
              v-for="item in enrollTypeAll"
              :label="item.value"
              :key="item.value"
              >{{ item.name }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>
        <el-form-item prop="master" label="学校联系人">
          <el-input
            size="small"
            v-model.trim="addForm.master"
            placeholder="请输入学校联系人"
            style="width: 260px"
          ></el-input>
        </el-form-item>
        <el-form-item prop="mobile" label="学校联系电话">
          <el-input
            size="small"
            v-model.trim="addForm.mobile"
            placeholder="请输入学校联系电话"
            style="width: 260px"
          ></el-input>
        </el-form-item>
        <el-form-item prop="content" label="学校简介">
          <div ref="editor"></div>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('addOrEdit', false)"
          >取消</el-button
        >
        <el-button size="small" type="primary" @click="confirmUpdate"
          >确定</el-button
        >
      </div>
    </el-dialog>

    <!-- 学校批量导入 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="modal.import"
      center
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form label-width="auto">
        <el-form-item label="模板文件">
          <el-button
            type="info"
            @click="downloadTemplateFile"
            icon="el-icon-download"
            size="small"
            >下载模板</el-button
          >
        </el-form-item>
        <el-form-item label="选择文件" prop="file">
          <el-upload
            ref="upload"
            accept=".xlsx,.xls"
            action=""
            :file-list="fileList"
            :auto-upload="false"
            :limit="1"
            :on-remove="onRemove"
            :on-exceed="onExceed"
            :on-change="onChange"
          >
            <el-button size="small" type="primary" icon="el-icon-folder-opened"
              >选择文件</el-button
            >
            <div slot="tip" class="warning-desc-text">
              只能上传excel文件，且不超过5M
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="错误信息" v-if="errorMessages.length > 0">
          <div style="max-height: 300px; overflow-y: auto">
            <div v-for="(item, index) in errorMessages" :key="index">
              <div class="error-desc-text">
                {{ index + 1 }}、第{{ item.rowIndex }}行：{{ item.message }}
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('import', false)"
          >取消</el-button
        >
        <el-button
          size="small"
          type="primary"
          @click="uploadSubmit"
          :disabled="fileList.length == 0"
          >确定</el-button
        >
      </div>
    </el-dialog>

    <!-- 账号批量导入 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="modal.importUser"
      center
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form label-width="auto">
        <el-form-item label="模板文件">
          <el-button
            type="info"
            @click="downloadTemplateFileUser"
            icon="el-icon-download"
            size="small"
            >下载模板</el-button
          >
        </el-form-item>
        <el-form-item label="选择文件" prop="file">
          <el-upload
            ref="upload"
            accept=".xlsx,.xls"
            action=""
            :file-list="fileListUser"
            :auto-upload="false"
            :limit="1"
            :on-remove="onRemoveUser"
            :on-exceed="onExceedUser"
            :on-change="onChangeUser"
          >
            <el-button size="small" type="primary" icon="el-icon-folder-opened"
              >选择文件</el-button
            >
            <div slot="tip" class="warning-desc-text">
              只能上传excel文件，且不超过5M
            </div>
          </el-upload>
        </el-form-item>
        <el-form-item label="错误信息" v-if="errorMessages.length > 0">
          <div style="max-height: 300px; overflow-y: auto">
            <div v-for="(item, index) in errorMessages" :key="index">
              <div class="error-desc-text">
                {{ index + 1 }}、第{{ item.rowIndex }}行：{{ item.message }}
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('importUser', false)"
          >取消</el-button
        >
        <el-button
          size="small"
          type="primary"
          @click="uploadSubmitUser"
          :disabled="fileListUser.length == 0"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <!--涉县设置每个类型报名时间-->
    <el-dialog
        :title="dialogTitle"
        :visible.sync="modal.settingTime"
        center
        width="800px"
        :close-on-click-modal="false"
    >
      <el-form :model="settingTimeForm" label-width="180px"  style="margin: auto;width: 500px">
        <el-form-item prop="beginTime" label="涉县城区户口报名开始时间">
          <el-date-picker
              size="small"
              v-model="settingTimeForm.beginTimeA"
              type="datetime"
              :editable="false"
              :clearable="true"
              value-format="yyyy-MM-dd HH:mm:ss"
              @change="confirmStart(settingTimeForm)"
              placeholder="请选择开始时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item prop="endTime" label="涉县城区户口结束时间">
          <el-date-picker
              size="small"
              v-model="settingTimeForm.endTimeA"
              type="datetime"
              :editable="false"
              :clearable="true"
              value-format="yyyy-MM-dd HH:mm:ss"
              @change="confirmEnd(settingTimeForm)"
              placeholder="请选择结束时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item prop="beginTime" label="涉县城区房产报名开始时间">
          <el-date-picker
              size="small"
              v-model="settingTimeForm.beginTimeB"
              type="datetime"
              :editable="false"
              :clearable="true"
              value-format="yyyy-MM-dd HH:mm:ss"
              @change="confirmStart(settingTimeForm)"
              placeholder="请选择开始时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item prop="endTime" label="涉县城区房产结束时间">
          <el-date-picker
              size="small"
              v-model="settingTimeForm.endTimeB"
              type="datetime"
              :editable="false"
              :clearable="true"
              value-format="yyyy-MM-dd HH:mm:ss"
              @change="confirmEnd(settingTimeForm)"
              placeholder="请选择结束时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item prop="beginTime" label="外来经商务工报名开始时间">
          <el-date-picker
              size="small"
              v-model="settingTimeForm.beginTimeC"
              type="datetime"
              :editable="false"
              :clearable="true"
              value-format="yyyy-MM-dd HH:mm:ss"
              @change="confirmStart(settingTimeForm)"
              placeholder="请选择开始时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item prop="endTime" label="外来经商务工结束时间">
          <el-date-picker
              size="small"
              v-model="settingTimeForm.endTimeC"
              type="datetime"
              :editable="false"
              :clearable="true"
              value-format="yyyy-MM-dd HH:mm:ss"
              @change="confirmEnd(settingTimeForm)"
              placeholder="请选择结束时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item prop="beginTime" label="截止修改开始时间">
          <el-date-picker
              size="small"
              v-model="settingTimeForm.stopBeginTime"
              type="datetime"
              :editable="false"
              :clearable="true"
              value-format="yyyy-MM-dd HH:mm:ss"
              @change="confirmStart(settingTimeForm)"
              placeholder="请选择开始时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item prop="endTime" label="截止修改结束时间">
          <el-date-picker
              size="small"
              v-model="settingTimeForm.stopEndTime"
              type="datetime"
              :editable="false"
              :clearable="true"
              value-format="yyyy-MM-dd HH:mm:ss"
              @change="confirmEnd(settingTimeForm)"
              placeholder="请选择结束时间"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div class="flex-center">
        <el-button size="small" @click="switchModal('settingTime', false)"
        >取消</el-button
        >
        <el-button
            size="small"
            type="primary"
            @click="settingTimeClick"
        >确定</el-button
        >
      </div>
    </el-dialog>

    <!-- 学校二维码管理对话框 -->
    <el-dialog
      title="学校二维码管理"
      :visible.sync="modal.qrcodeManage"
      width="1200px"
      :close-on-click-modal="false">
      <div v-if="currentSchool" style="margin-bottom: 20px;">
        <strong>当前学校：{{ currentSchool.deptName }}</strong>
      </div>

      <div class="filter-container" style="margin-bottom: 20px;">
        <el-button type="primary" icon="el-icon-plus" @click="handleQrcodeCreate">
          新增二维码
        </el-button>
      </div>

      <el-table
        v-loading="qrcodeListLoading"
        :data="qrcodeList"
        element-loading-text="加载中..."
        border
        fit
        highlight-current-row
        style="width: 100%">
        <el-table-column label="序号" align="center" width="80">
          <template slot-scope="scope">
            <span>{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="ID" align="center" width="220">
          <template slot-scope="scope">
            <span>{{ scope.row.qrcodeId }}</span>
          </template>
        </el-table-column>
        <el-table-column label="二维码图片" align="center" min-width="150">
          <template slot-scope="scope">
            <el-image
              v-if="scope.row.qrcodeImage"
              style="width: 100px; height: 100px"
              :src="getQrcodeImageUrl(scope.row.qrcodeImage)"
              :preview-src-list="[getQrcodeImageUrl(scope.row.qrcodeImage)]">
            </el-image>
            <span v-else>暂无图片</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="danger"
              @click="handleQrcodeDelete(scope.row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 新增二维码对话框 -->
      <el-dialog title="新增学校二维码" :visible.sync="qrcodeDialogVisible" append-to-body>
        <el-form ref="qrcodeDataForm" :model="qrcodeTemp" label-position="left" label-width="100px" style="width: 80%; margin: 0 auto;">
          <el-form-item label="二维码图片">
            <el-upload
              action=""
              :http-request="uploadQrcodeImage"
              :show-file-list="true"
              :before-upload="beforeQrcodeUpload"
              :limit="10"
              multiple
              list-type="picture-card"
              :file-list="qrcodeFileList"
              :on-remove="handleQrcodeRemove">
              <i class="el-icon-plus"></i>
              <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过2MB</div>
            </el-upload>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="qrcodeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="createQrcodeData">确认</el-button>
        </div>
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script>
import TableMixin from "@/mixins/TableMixin";
import ModalMixin from "@/mixins/ModalMixin";
import {
  schoolList,
  uptSchool,
  addSchool,
  schoolDetail,
  delSchool,
  enableSchool,
  disableSchool,
  importSchool,
  importSchools,
  getTimeDetail,
  saveSchoolTime
} from "@/api/setting";
import Editor from "wangeditor";
import { importUser ,importUsers} from "@/api/user";
import { pref } from "@/utils/common";

export default {
  mixins: [TableMixin, ModalMixin],
  data() {
    return {
      isFive: this.$store.getters.userInfo.isFive,
      prefixDeptCode: this.$store.getters.deptCode,
      baseApi: process.env.VUE_APP_BASE_API,
      search: {
        keywords: "",
        nature: "",
        period: "",
        status: "",
     /*    type: 1,*/
        level: 3,
      },
      addForm: {
        id: "",
        deptName: "",
        address: "",
        nature: "",
        type:"",
        period: "",
        planNum: "",
        master: "",
        mobile: "",
        content: "",
        enrollType: "",
        enrollTypeList: [],
        hukouNum:0,
        houseNum:0,
        businessNum:0,
        schoolBiaoShi:""
      },
      settingTimeForm:{
        beginTimeA:'',
        endTimeA:'',
        beginTimeB:'',
        endTimeB:'',
        beginTimeC:'',
        endTimeC:'',
        stopBeginTime:'',
        stopEndTime:''
      },
      rules: {
        deptName: [
          {
            required: true,
            message: "请输入学校名称",
            trigger: "blur",
          },
        ],
        address: [
          {
            required: true,
            message: "请输入学校地址",
            trigger: "blur",
          },
        ],
        nature: [
          {
            required: true,
            message: "请选择学校性质",
            trigger: "change",
          },
        ],
       type:[
          {
            required: true,
            message: "请选择学校类型",
            trigger: "change",
          },
        ],
        period: [
          {
            required: true,
            message: "请选择学段",
            trigger: "change",
          },
        ],
        planNum: [
          {
            required: true,
            message: "请输入招生计划数",
            trigger: "blur",
          },
          {
            type: "number",
            message: "计划数必须为数字值",
          },
        ],
        hukouNum: [
          {
            required: true,
            message: "请输入户口-招生计划数",
            trigger: "blur",
          },
          {
            type: "number",
            message: "计划数必须为数字值",
          },
        ],
        houseNum: [
          {
            required: true,
            message: "请输入房产-招生计划数",
            trigger: "blur",
          },
          {
            type: "number",
            message: "计划数必须为数字值",
          },
        ],
        businessNum: [
          {
            required: true,
            message: "请输入经商务工-招生计划数",
            trigger: "blur",
          },
          {
            type: "number",
            message: "计划数必须为数字值",
          },
        ],
        master: [
          {
            required: true,
            message: "请输入联系人",
            trigger: "blur",
          },
        ],
        mobile: [
          {
            required: true,
            message: "请输入联系电话",
            trigger: "blur",
          },
        ],
        content: [
          {
            required: true,
            message: "请输入学校简介",
            trigger: "change",
          },
        ],
        enrollTypeList: [
          {
            required: true,
            message: "请选择招生类别",
            trigger: "change",
          },
        ],
      },
      dialogTitle: "新增学校",
      modal: {
        addOrEdit: false,
        import: false,
        importUser: false,
        settingTime:false,
        qrcodeManage: false
      },
      fileList: [],
      errorMessages: [],
      editor: null,
      fileListUser: [],
      exportLoading: false,
      qrcodeExportLoading: false,

      enrollTypeAll: [
        {
          value: "A",
          name: "房户一致",
        },
        {
          value: "B",
          name: "户口",
        },
        {
          value: "C",
          name: "房产",
        },
        {
          value: "D",
          name: "随迁子女",
        },
        {
          value: "E",
          name: "优抚对象",
        },
        {
          value: "F",
          name: "本县经商",
        },
        {
          value: "G",
          name: "本县务工",
        },
        {
          value: "H",
          name: "其他",
        },
        {
          value: "I",
          name: "优抚对象",
        },
        {
          value: "G",
          name: "有户口无房产",
        }
      ],
      // 二维码管理相关数据
      currentSchool: null,
      qrcodeList: [],
      qrcodeListLoading: false,
      qrcodeFileList: [],
      qrcodeUploadedFiles: [],
      qrcodeTemp: {
        schoolId: undefined,
        qrcodeImage: ''
      },
      qrcodeDialogVisible: false,
    };
  },
  computed: {
    switchEnrollType() {
      return (val) => {
        // return (val == "1" ? "启用" : "禁用");
        let array = [];
        let str = '';
        if(val!=null){
          if(val.includes("A")) {
            array.push("房户一致")
          }
          if(val.includes("B")) {
            array.push("户口")
          }
          if(val.includes("C")) {
            array.push("房产")
          }
          if(val.includes("D")) {
            array.push("随迁子女")
          }
          if(val.includes("E")&&this.prefixDeptCode!=="130225") {
            array.push("优抚对象")
          }
          if(val.includes("F")) {
            array.push("本县经商")
          }
          if(val.includes("G")) {
            array.push("本县务工")
          }
          if(val.includes("H")) {
            array.push("其他")
          }
          if(val.includes("I")) {
            array.push("优抚对象")
          }
          str = array.join(',')
        }

        return str
      }
    }
  },
  created() {
    // sessionStorage.removeItem("schoolDetail");
  },
  destroyed() {
    if (this.editor) {
      this.editor.destroy();
    }
  },
  methods: {
    settingTimeClick(){
      saveSchoolTime(this.settingTimeForm,this.prefixDeptCode).then(res=>{
       if(res){
         this.switchModal("settingTime", false);
         this.$message.success('设置时间成功')
       }
     })
    },
    settingTime(row){
      this.modal.settingTime=true
      getTimeDetail({key:row.id},this.prefixDeptCode).then(res=>{
        this.settingTimeForm=res
        this.settingTimeForm.schoolId=row.id
      })
    },
    confirmStart(item,index){
      let astartTime = new Date(item.beginTimeA).getTime()
      let endTimeA = new Date(item.endTimeA).getTime()
      let bstartTime = new Date(item.beginTimeB).getTime()
      let endTimeB = new Date(item.endTimeB).getTime()
      let cstartTime = new Date(item.beginTimeC).getTime()
      let endTimeC = new Date(item.endTimeC).getTime()
      if(item.beginTimeA&&item.endTimeA){

        if(astartTime>endTimeA){
          this.$message.error('开始时间不能大于结束时间')
            this.settingTimeForm.beginTimeA=''
        }
      }
      if(item.beginTimeB&&item.endTimeB){

        if(bstartTime>endTimeB){
          this.$message.error('开始时间不能大于结束时间')
          this.settingTimeForm.beginTimeB=''
        }
      }if(item.beginTimeC&&item.endTimeC){

        if(cstartTime>endTimeC){
          this.$message.error('开始时间不能大于结束时间')
          this.settingTimeForm.beginTimeC=''
        }
      }
    },
    confirmEnd(item,index){
      let astartTime = new Date(item.beginTimeA).getTime()
      let endTimeA = new Date(item.endTimeA).getTime()
      let bstartTime = new Date(item.beginTimeB).getTime()
      let endTimeB = new Date(item.endTimeB).getTime()
      let cstartTime = new Date(item.beginTimeC).getTime()
      let endTimeC = new Date(item.endTimeC).getTime()
      if(item.beginTimeA&&item.endTimeA){

        if(astartTime>endTimeA){
          this.$message.error('结束时间不能小于开始时间')
          this.settingTimeForm.endTimeA=''
        }
      }
      if(item.beginTimeB&&item.endTimeB){

        if(bstartTime>endTimeB){
          this.$message.error('结束时间不能小于开始时间')
          this.settingTimeForm.endTimeB=''
        }
      }if(item.beginTimeC&&item.endTimeC){

        if(cstartTime>endTimeC){
          this.$message.error('结束时间不能小于开始时间')
          this.settingTimeForm.endTimeC=''
        }
      }
    },
    // 列表
    getTableData() {
     /* this.search.type=''*/
      schoolList(this.search).then((data) => (this.tableData = data));
    },
    // 添加
    addSchool() {
      this.dialogTitle = "新增学校";
      this.switchModal("addOrEdit", true);
      this.$nextTick(() => {
        this.$refs.addForm.resetFields();
        this.addForm = {
          id: "",
          deptName: "",
          address: "",
          nature: "",
          type: "",
          period: "",
          planNum: "",
          master: "",
          mobile: "",
          content: "",
          enrollType: "",
          enrollTypeList: [],
        };
        if (this.editor == null) {
          this.editorCreate();
        } else {
          this.editor.destroy();
          this.editorCreate();
        }
      });
    },
    // 编辑
    async edit(row) {
      this.dialogTitle = "编辑学校";
      this.switchModal("addOrEdit", true);

      // 重置表单
      this.$nextTick(() => {
        this.$refs.addForm.resetFields();

        // 获取学校详情
        schoolDetail({ key: row.id }).then(({
                                              id,
                                              deptName,
                                              address,
                                              nature,
                                              type,
                                              period,
                                              planNum,
                                              master,
                                              mobile,
                                              content,
                                              enrollType,
                                              schoolBiaoShi,
                                            }) => {
          this.addForm = {
            id,
            deptName,
            address,
            nature,
            type,
            period,
            planNum,
            master,
            mobile,
            content,
            enrollType,
            enrollTypeList: enrollType ? enrollType.split(",") : [],
            hukouNum: row.hukouNum || 0,
            houseNum: row.houseNum || 0,
            businessNum: row.businessNum || 0,
            schoolBiaoShi: schoolBiaoShi || ""
          };

          // 确保DOM更新完成后再初始化编辑器
          this.$nextTick(() => {
            // 销毁旧编辑器
            if (this.editor) {
              this.editor.destroy();
              this.editor = null;
            }

            // 创建新编辑器
            this.editorCreate();

            // 设置内容
            if (content) {
              this.editor.txt.html(content);
            }
          });
        });
      });
    },
    // 账号管理
    acctManage(row) {
      // sessionStorage.setItem("schoolDetail", JSON.stringify(row));
      this.$router.push({
        path: "/setting/schoolManageAcct",
        query: {
          id: row.id,
          period: row.period,
          deptName: row.deptName,
        },
      });
    },
    // 学校二维码管理
    manageQrcode(row) {
      console.log('打开学校二维码管理，学校信息:', row);
      console.log('当前用户角色:', this.$store.getters.role);
      console.log('当前部门代码:', this.prefixDeptCode);

      this.currentSchool = row;
      this.switchModal("qrcodeManage", true);
      this.getQrcodeList();
    },
    // 启用
    enable(row) {
      enableSchool({
        key: row.id,
      }).then((res) => {
        this.$message.success("操作成功");
        this.getTableData();
      });
    },
    // 禁用
    disable(row) {
      disableSchool({
        key: row.id,
      }).then((res) => {
        this.$message.success("操作成功");
        this.getTableData();
      });
    },
    // 删除
    del(row) {
      this.$confirm(`确认删除${row.deptName}？`, "删除学校", {
        type: "error",
      }).then(() => {
        delSchool({
          key: row.id,
        }).then((res) => {
          this.$message.success("操作成功");
          this.getTableData();
        });
      });
    },
    // 确认更新
    confirmUpdate() {
      if (this.isFive) {
        this.addForm.period = 2;
        this.addForm.type = 1;
        this.addForm.nature = 2;
      }
      if(this.prefixDeptCode=='130426'){
        this.addForm.enrollTypeList=['A','B','C']
      }
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          this.addForm.enrollType = this.addForm.enrollTypeList.join(",");
          if (this.addForm.id) {
            uptSchool(this.addForm).then((res) => {
              this.switchModal("addOrEdit", false);
              this.$message.success("操作成功");
              this.getTableData();
            });
          } else {
            addSchool(this.addForm).then((res) => {
              this.switchModal("addOrEdit", false);
              this.$message.success("操作成功");
              this.getTableData();
            });
          }
        }
      });
    },
    // 学校批量导入
    importBatch() {
      this.switchModal("import", true);
      this.dialogTitle = "学校批量导入";
      this.fileList = [];
      this.errorMessages = [];
    },
    // 下载模版
    downloadTemplateFile() {
      if(this.$store.getters.deptCode == "130207") {
        this.$download(
            "/user-api/center/dept/downTemplates",
            {},
            "xls",
            "学校导入模版.xls"
        ).then((res) => {
          this.$message.success("下载成功");
        });
      }else {
        this.$download(
            "/user-api/center/dept/downTemplate",
            {},
            "xls",
            "学校导入模版.xls"
        ).then((res) => {
          this.$message.success("下载成功");
        });
      }
    },
    onRemove(file, fileList) {
      this.fileList = fileList;
      this.errorMessages = [];
    },
    onExceed(files, fileList) {
      this.$message.warning("最多上传1个文件");
    },
    onChange(file, fileList) {
      let raw = file.raw;
      let fileTp =
        raw.type == "application/vnd.ms-excel" ||
        raw.type ==
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      if (!fileTp) {
        this.$message.warning("请上传excel格式");
        this.fileList.splice(0, 1);
      } else {
        if (file.size > 5 * 1024 * 1024) {
          this.$message.warning("上传限制文件大小不能大于5M");
          return false;
        }
        this.fileList.push(file.raw);
      }
    },
    // 导入提交
    uploadSubmit() {
      let formData = new FormData();
      formData.append("file", this.fileList[0]);
      if(this.$store.getters.deptCode == "130207") {
        importSchools(formData).then((res) => {
          if (res.length > 0) {
            this.errorMessages = res;
          } else {
            this.$message.success("导入成功");
            this.switchModal("import", false);
            this.getTableData();
          }
        });
      }else{
      importSchool(formData).then((res) => {
        if (res.length > 0) {
          this.errorMessages = res;
        } else {
          this.$message.success("导入成功");
          this.switchModal("import", false);
          this.getTableData();
        }
      });
      }
    },
    // 创建富文本
    editorCreate() {
      var _this = this;
      this.editor = new Editor(this.$refs.editor);
      this.editor.config.onchange = (html) => this.editorCatchData(html);
      this.editor.config.height = 400;
      this.editor.config.showLinkImg = false;
      this.editor.config.debug = true;
      this.editor.config.uploadImgServer = `${this.baseApi}/user-api/user/file/uploadImg`;
      this.editor.config.uploadFileName = "file";
      this.editor.config.uploadImgMaxSize = 5 * 1024 * 1024;
      this.editor.config.uploadImgHeaders = {
        Authorization: `${this.$store.getters.token}`,
        "client-id": "admin",
        state: this.$store.getters.securityKey,
      };
      this.editor.config.uploadImgHooks = {
        fail: function (xhr, editor, result) {},
        success: function (xhr, editor, result) {},
        customInsert: function (insertImgFn, result) {
          insertImgFn(_this.baseApi + "/user-api" + result.data);
        },
      };
      this.editor.config.menus = [
        // 菜单配置
        "head", // 标题
        "bold", // 粗体
        "fontSize", // 字号
        "fontName", // 字体
        "italic", // 斜体
        "underline", // 下划线
        "strikeThrough", // 删除线
        "foreColor", // 文字颜色
        "backColor", // 背景颜色
        "link", // 插入链接
        "list", // 列表
        "justify", // 对齐方式
        "quote", // 引用
        "emoticon", // 表情
        "image", // 插入图片
        "table", // 表格
        "code", // 插入代码
        "undo", // 撤销
        "redo", // 重复
      ];
      // 创建富文本实例
      this.editor.create();
    },
    // 更新content
    editorCatchData(content) {
      this.addForm.content = content;
    },

    // 账号批量导入
    importBatchUser() {
      this.switchModal("importUser", true);
      this.dialogTitle = "账号批量导入";
      this.fileListUser = [];
      this.errorMessages = [];
    },

      // 账号批量导入下载模版
      downloadTemplateFileUser() {
        if(this.$store.getters.deptCode == "130207") {

          this.$download(
              "/user-api/center/user/downTemplates",
              {},
              "xls",
              "账号批量导入模版.xls"
          ).then((res) => {
            this.$message.success("下载成功");
          });
        }else {


          this.$download(
              "/user-api/center/user/downTemplate",
              {},
              "xls",
              "账号批量导入模版.xls"
          ).then((res) => {
            this.$message.success("下载成功");
          });
        }
      },


    onRemoveUser(file, fileList) {
      this.fileListUser = fileList;
      this.errorMessages = [];
    },
    onExceedUser(files, fileList) {
      this.$message.warning("最多上传1个文件");
    },
    onChangeUser(file, fileList) {
      let raw = file.raw;
      let fileTp =
        raw.type == "application/vnd.ms-excel" ||
        raw.type ==
          "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      if (!fileTp) {
        this.$message.warning("请上传excel格式");
        this.fileListUser.splice(0, 1);
      } else {
        if (file.size > 5 * 1024 * 1024) {
          this.$message.warning("上传限制文件大小不能大于5M");
          return false;
        }
        this.fileListUser.push(file.raw);
      }
    },
    // 账号批量导入提交
    uploadSubmitUser() {
      let formData = new FormData();
      formData.append("file", this.fileListUser[0]);
      if(this.$store.getters.deptCode == "130207") {
        importUser(formData).then((res) => {
          if (res.length > 0) {
            this.errorMessages = res;
          } else {
            this.$message.success("导入成功");
            this.switchModal("importUser", false);
          }
        });
      }else{
        importUser(formData).then((res) => {
          if (res.length > 0) {
            this.errorMessages = res;
          } else {
            this.$message.success("导入成功");
            this.switchModal("importUser", false);
          }
        });
      }

    },
    // 账号导出
    exportListUser() {
      this.exportLoading = true;
      let params = this.search;
      this.$download(
        "/user-api/center/user/exportUserList",
        params,
        "xls",
        "导出账号列表.xls"
      ).then((res) => {
        this.exportLoading = false;
        this.$message.success("导出成功");
      });
    },
    // 导出学校二维码
    exportQrcodeList() {
      this.qrcodeExportLoading = true;
      console.log('开始导出学校二维码，当前用户角色:', this.$store.getters.role);
      console.log('当前部门代码:', this.prefixDeptCode);

      this.$download(
        "/user-api/center/qrcode/exportQrcodeList",
        {},
        "xls",
        "学校二维码列表.xls"
      ).then((res) => {
        this.qrcodeExportLoading = false;
        this.$message.success("导出成功");
      }).catch((error) => {
        console.error('导出失败详细错误:', error);
        this.qrcodeExportLoading = false;
        this.$message.error("导出失败: " + (error.message || '未知错误'));
      });
    },
    // 二维码管理相关方法
    getQrcodeList() {
      if (!this.currentSchool) return;

      this.qrcodeListLoading = true;
      const { getSchoolQrcodeList } = require('@/api/schoolQrcode');
      getSchoolQrcodeList({key: this.currentSchool.id}).then(response => {
        console.log('获取二维码列表响应:', response);

        if (Array.isArray(response)) {
          this.qrcodeList = response;
        } else if (response && response.data && Array.isArray(response.data)) {
          this.qrcodeList = response.data;
        } else if (response && response.data) {
          this.qrcodeList = [response.data];
        } else {
          this.qrcodeList = [];
        }

        this.qrcodeListLoading = false;
      }).catch((error) => {
        console.error('获取二维码列表数据错误:', error);
        this.qrcodeListLoading = false;
      });
    },
    getQrcodeImageUrl(url) {
      if (!url) return '';
      if (url.startsWith('http')) {
        return url;
      }
      const { pref } = require('@/utils/common');
      return `${process.env.VUE_APP_BASE_API}${pref}${this.prefixDeptCode}${url}`;
    },
    handleQrcodeCreate() {
      this.resetQrcodeTemp();
      this.qrcodeDialogVisible = true;
      this.$nextTick(() => {
        this.$refs['qrcodeDataForm'] && this.$refs['qrcodeDataForm'].clearValidate();
      });
    },
    resetQrcodeTemp() {
      this.qrcodeTemp = {
        schoolId: this.currentSchool ? this.currentSchool.id : undefined,
        qrcodeImage: ''
      };
      this.qrcodeFileList = [];
      this.qrcodeUploadedFiles = [];
    },
    handleQrcodeDelete(row) {
      this.$confirm('确认删除该条记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const { deleteSchoolQrcode } = require('@/api/schoolQrcode');
        deleteSchoolQrcode({key: row.id}).then(() => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
          this.getQrcodeList();
        });
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    beforeQrcodeUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error('上传图片只能是 JPG/PNG 格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!');
      }
      return isJPG && isLt2M;
    },
    uploadQrcodeImage(param) {
      const { uploadPicture } = require('@/utils/upload');

      uploadPicture(param, this.prefixDeptCode).then(response => {
        console.log('上传响应:', response);
        if (response) {
          this.qrcodeUploadedFiles.push(response);
          // 不需要显示成功消息，uploadPicture函数内部已经显示了
        }
      }).catch(error => {
        console.error('上传失败:', error);
        this.$message.error('上传失败');
      });
    },
    handleQrcodeRemove(file, fileList) {
      this.qrcodeFileList = fileList;
      // 由于使用uploadPicture，返回值直接是路径字符串
      const index = this.qrcodeUploadedFiles.findIndex(url => {
        // 检查file.url是否包含url路径
        return file.url && file.url.includes(url);
      });
      if (index > -1) {
        this.qrcodeUploadedFiles.splice(index, 1);
      }
    },
    createQrcodeData() {
      if (this.qrcodeUploadedFiles.length === 0) {
        this.$message.error('请上传二维码图片');
        return;
      }

      const { addSchoolQrcode, batchAddSchoolQrcode } = require('@/api/schoolQrcode');

      if (this.qrcodeUploadedFiles.length === 1) {
        // 单个上传
        const tempData = Object.assign({}, this.qrcodeTemp);
        tempData.qrcodeImage = this.qrcodeUploadedFiles[0];

        addSchoolQrcode(tempData).then(() => {
          this.qrcodeDialogVisible = false;
          this.$message({
            message: '创建成功',
            type: 'success'
          });
          this.getQrcodeList();
        });
      } else {
        // 批量上传
        const data = {
          schoolId: this.qrcodeTemp.schoolId,
          imageUrls: this.qrcodeUploadedFiles
        };

        batchAddSchoolQrcode(data).then(() => {
          this.qrcodeDialogVisible = false;
          this.$message({
            message: '批量创建成功',
            type: 'success'
          });
          this.getQrcodeList();
        });
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.search-form {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
