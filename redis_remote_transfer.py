#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
不同Redis服务器之间的数据转移
"""

import redis
import time

# ==================== 配置区域 ====================

# 源Redis服务器配置
SOURCE_REDIS = {
    'host': '*************',    # 源Redis IP地址
    'port': 6379,               # 源Redis端口
    'password': 'source_pass',  # 源Redis密码，无密码设为None
    'db': 0,                    # 源数据库编号
}

# 目标Redis服务器配置
TARGET_REDIS = {
    'host': '*************',    # 目标Redis IP地址
    'port': 6379,               # 目标Redis端口
    'password': 'target_pass',  # 目标Redis密码，无密码设为None
    'db': 0,                    # 目标数据库编号
}

# 转移选项
OPTIONS = {
    'copy_mode': True,          # True=复制模式, False=移动模式
    'overwrite': False,         # True=覆盖已存在的键
    'batch_size': 200,          # 批处理大小
    'show_detail': False,       # 是否显示每个键的详细信息
}

# ==================== 代码区域 ====================

def create_redis_client(config):
    """创建Redis客户端"""
    return redis.Redis(
        host=config['host'],
        port=config['port'],
        password=config['password'],
        db=config['db'],
        decode_responses=False,
        socket_timeout=30,
        socket_connect_timeout=30
    )

def test_connection(client, name):
    """测试Redis连接"""
    try:
        client.ping()
        print(f"✓ {name}连接成功")
        return True
    except Exception as e:
        print(f"✗ {name}连接失败: {e}")
        return False

def main():
    print("Redis服务器间数据转移工具")
    print("="*50)
    
    # 显示配置
    print("源Redis:", f"{SOURCE_REDIS['host']}:{SOURCE_REDIS['port']} DB{SOURCE_REDIS['db']}")
    print("目标Redis:", f"{TARGET_REDIS['host']}:{TARGET_REDIS['port']} DB{TARGET_REDIS['db']}")
    print("模式:", "复制" if OPTIONS['copy_mode'] else "移动")
    print("覆盖:", "是" if OPTIONS['overwrite'] else "否")
    print("-" * 50)
    
    # 创建Redis客户端
    source_client = create_redis_client(SOURCE_REDIS)
    target_client = create_redis_client(TARGET_REDIS)
    
    # 测试连接
    if not test_connection(source_client, "源Redis"):
        return
    if not test_connection(target_client, "目标Redis"):
        return
    
    # 获取数据库信息
    try:
        source_size = source_client.dbsize()
        target_size = target_client.dbsize()
        
        print(f"\n当前状态:")
        print(f"源数据库键数: {source_size}")
        print(f"目标数据库键数: {target_size}")
        
        if source_size == 0:
            print("源数据库为空，无需转移")
            return
            
    except Exception as e:
        print(f"获取数据库信息失败: {e}")
        return
    
    # 确认执行
    action = "移动" if not OPTIONS['copy_mode'] else "复制"
    confirm = input(f"\n确认{action} {source_size} 个键到目标服务器? (y/N): ")
    if confirm.lower() != 'y':
        print("取消转移")
        return
    
    # 开始转移
    print(f"\n开始{action}数据...")
    start_time = time.time()
    
    # 扫描所有键
    print("正在扫描源数据库...")
    all_keys = []
    cursor = 0
    
    while True:
        try:
            cursor, keys = source_client.scan(cursor=cursor, count=OPTIONS['batch_size'])
            all_keys.extend(keys)
            if cursor == 0:
                break
        except Exception as e:
            print(f"扫描键失败: {e}")
            return
    
    total_keys = len(all_keys)
    print(f"找到 {total_keys} 个键")
    
    # 转移数据
    transferred = 0
    skipped = 0
    failed = 0
    
    for i, key in enumerate(all_keys, 1):
        try:
            # 检查目标是否存在
            if target_client.exists(key) and not OPTIONS['overwrite']:
                if OPTIONS['show_detail']:
                    print(f"[{i}/{total_keys}] 跳过: {key.decode('utf-8', errors='ignore')}")
                skipped += 1
                continue
            
            # 导出数据
            dump_data = source_client.dump(key)
            if dump_data is None:
                skipped += 1
                continue
            
            # 获取TTL
            ttl = source_client.pttl(key)
            if ttl == -2:  # 键不存在
                skipped += 1
                continue
            elif ttl == -1:  # 无过期时间
                ttl = 0
            
            # 覆盖模式下先删除目标键
            if OPTIONS['overwrite'] and target_client.exists(key):
                target_client.delete(key)
            
            # 导入到目标Redis
            target_client.restore(key, ttl, dump_data)
            
            # 移动模式下删除源键
            if not OPTIONS['copy_mode']:
                source_client.delete(key)
            
            transferred += 1
            
            # 显示进度
            if OPTIONS['show_detail']:
                print(f"[{i}/{total_keys}] {action}: {key.decode('utf-8', errors='ignore')}")
            elif i % 100 == 0 or i == total_keys:
                print(f"进度: {i}/{total_keys} ({i/total_keys*100:.1f}%)")
                
        except Exception as e:
            failed += 1
            if OPTIONS['show_detail']:
                print(f"[{i}/{total_keys}] 失败: {key.decode('utf-8', errors='ignore')} - {e}")
    
    end_time = time.time()
    
    # 输出结果
    print(f"\n{action}完成!")
    print(f"总键数: {total_keys}")
    print(f"成功{action}: {transferred}")
    print(f"跳过: {skipped}")
    print(f"失败: {failed}")
    print(f"耗时: {end_time - start_time:.2f} 秒")
    
    # 验证最终状态
    try:
        final_source = source_client.dbsize()
        final_target = target_client.dbsize()
        
        print(f"\n最终状态:")
        print(f"源数据库键数: {final_source}")
        print(f"目标数据库键数: {final_target}")
        
        if OPTIONS['copy_mode']:
            print("✓ 复制完成，源数据保留")
        else:
            print("✓ 移动完成，源数据已删除")
            
    except Exception as e:
        print(f"验证最终状态失败: {e}")

if __name__ == "__main__":
    main()
